<!DOCTYPE html>
<html>
  <head>
    <meta charset="utf-8" />
    <title>
      Pomelo
    </title>
    <meta http-equiv="content-type" content="text/html;charset=utf-8" />
    <meta http-equiv="content-style-type" content="text/css" />
    <meta http-equiv="content-scripte-type" content="text/javascript" />
    <meta name="author" content="netease" />
    <meta name="version" content="1.0" />
    <meta name="keywords" content="pomelo" />
    <link type="text/css" rel="stylesheet" href="css/base.css" />
    <script src="js/lib/socket.io.js">
    </script>
    <!-- <script src="js/lib/pomeloclient.js"></script> -->
    <script src="js/lib/build/build.js" type="text/javascript"></script>
    <script type="text/javascript">
      require('boot');
    </script> 
    
    <script type="text/javascript">
      var pomelo = window.pomelo;
      var host = "127.0.0.1"; // "************"; // "127.0.0.1";
      var port = "3014"; // "3010"; "3014";
      var registeredTournaments = {}; // Lưu trữ thông tin tournament đã đăng ký theo account
      function show(deviceId) {
        console.log("start pomelo");
        pomelo.init({
          host: host,
          port: port,
          log: true
        }, function() {
          // get 
          pomelo.request("gate.gateHandler.queryEntry", {uid: 1}, function(data) {
            console.log('dataMINH: ', data);
            const _host = data?.host ?? '';
            const _port = data?.port ?? '';
            pomelo.disconnect();

            pomelo.init({
              host: _host,
              port: _port,
              log: true
            }, function() {
                console.log("start pomelo again");
                // call login: test, minh
                pomelo.request("connector.entryHandler.loginDevice", {deviceId: deviceId}, function(data) {
                  console.log('data: ', data);
                  const userId = data?.player?.user_id ?? '';
                  console.log('- userId: ', userId);

                  // Create table
                  // ----------------------------------------------------------------
                  var dataPost = {
                    "smallBlind":"50",
                    "bigBlind":"100",
                    "minBuyIn":"100",
                    "maxBuyIn":"10000",
                    "minPlayers":"2",
                    "maxPlayers":"10",
                    "gameMode":"normal",
                    "zone": "CC",
                  };
                  const createTable = 'game.tableHandler.createTable';
                  // pomelo.request(createTable, dataPost, function(data) {
                    // console.log('createTable >> data: ', data);
                  // });

                  // Get list tables
                  // pomelo.request('game.tableHandler.getTables', '', function(data) {
                    // console.log('getTables >> data: ', data);
                  // });

                  // Get list tables by zone = TS
                  pomelo.request('game.tableHandler.getTables', {zone: 'TS'}, function(data) {
                    // console.log('getTables with zone = TS >> data: ', data.tables);
                    const tables = data?.tables?.tables ?? [];
                    console.log('getTables with zone = TS >> tables: ', tables);
                    if (tables.length > 0) {
                      // const randomTable = tables[Math.floor(Math.random() * tables.length)];
                      const randomTable = tables[0];
                      console.log('Random table: ', randomTable);
                      
                      // join table
                      const tid = randomTable?.id ?? null;
                      const minBuyIn = randomTable?.minBuyIn ?? 0;
                      const maxBuyIn = randomTable?.maxBuyIn ?? 0;

                      const cmdJoinTable = 'game.tableHandler.joinTable';
                      pomelo.request(cmdJoinTable, {tid: tid}, function(data) {
                        console.log('joinTable >> data: ', data);

                        // joinGame: ngồi xuống bàn
                        const cmdJoinGame = 'game.tableHandler.joinGame';
                        const buyIn = maxBuyIn - minBuyIn;

                        console.log('-> maxBuyIn: ', maxBuyIn);
                        console.log('-> minBuyIn: ', minBuyIn);
                        console.log('-> buyIn: ', buyIn);
                        pomelo.request(cmdJoinGame, {buyIn: buyIn, index: 1}, function(data) {
                          console.log('joinGame >> data: ', data);
                          if (Number(data.code) === 200) {
                            // Command đứng dậy
                            // =================================================================
                            const cmdStandUp = 'game.tableHandler.standUp';
                            pomelo.request(cmdStandUp, '', function(data) {
                              console.log('standUp >> data: ', data);
                            });
                            // =================================================================

                            // Command Get Thông tin 1 user
                            // =================================================================
                            /*
                            const cmdGetUserInfo = 'game.userHandler.getUserInfo';
                            pomelo.request(cmdGetUserInfo, {uid: userId}, function(data) {
                              console.log('- getUserInfo >> data: ', data);
                            });
                            */
                            // =================================================================

                            // Command: Get danh sách users đang ở lobby để mời chời
                            // =================================================================
                            const cmdGetUsersInLobby = 'game.tableHandler.getUsersInLobby';
                            pomelo.request(cmdGetUsersInLobby, {uid: userId}, function(data) {
                              console.log('- getUsersInLobby >> data: ', data);
                            });
                            // =================================================================

                            // Thoát khỏi phòng
                            // =================================================================
                            /*
                            const cmdLeaveTable = 'game.tableHandler.leaveTable';
                            pomelo.request(cmdLeaveTable, '', function(data) {
                              console.log('leaveTable >> data: ', data);
                              
                              if (Number(data.code) === 200) {
                                console.log('Leave table successfully => chuẩn bị logout');
                                // send command: Logout
                                // =================================================================
                                const cmdLogout = 'connector.entryHandler.logout';
                                console.log('Logout with userId: ', userId);
                                pomelo.request(cmdLogout, {uid: userId}, function(dt) {
                                  console.log('logout >> data: ', dt);
                                });
                                // =================================================================
                              }
                              
                            });
                            */
                            // =================================================================
                          }
                        });

                      });

                    } else {
                      console.log('No tables available');
                    }
                  });

                  // Get list rooms
                  // const cmdListRooms = 'game.tableHandler.getRooms';
                  // pomelo.request(cmdListRooms, '', function(data) {
                  //  console.log('getRooms >> data: ', data);
                  // });
                  
                }); // end handler loginDevice
            });
          });
        });
      } // end show

      // show 2
      function show2() {
        console.log("start pomelo");
        pomelo.init({
          host: host,
          port: port,
          log: true
        }, function() {
          
          pomelo.request("gate.gateHandler.queryEntry", {uid: 1}, function(data) {

            console.log('dataMINH: ', data);
            const _host = data?.host ?? '';
            const _port = data?.port ?? '';
            pomelo.disconnect();

            pomelo.init({
              host: _host,
              port: _port,
              log: true
            }, function() {

              pomelo.request("connector.entryHandler.loginDevice", {deviceId: "minh4"}, function(data) {
                console.log('data: ', data);
                const userId = data?.player?.user_id ?? '';
                console.log('- userId: ', userId);

                // Get list tables by zone = TS
                pomelo.request('game.tableHandler.getTables', {zone: 'TS'}, function(data) {
                  // console.log('getTables with zone = TS >> data: ', data.tables);
                  const tables = data?.tables?.tables ?? [];
                  console.log('getTables with zone = TS >> tables: ', tables);
                  if (tables.length > 0) {
                    const randomTable = tables[Math.floor(Math.random() * tables.length)];
                    console.log('Random table: ', randomTable);
                    
                    // join table
                    const tid = randomTable?.id ?? null;
                    const minBuyIn = randomTable?.minBuyIn ?? 0;
                    const maxBuyIn = randomTable?.maxBuyIn ?? 0;

                  } else {
                    console.log('No tables available');
                  }
                });


              });

          });
      });
      
        });
      } // end login to lobby
      
      function switchTable() {
        console.log("start pomelo switchTable");
        pomelo.init({
          host: host,
          port: port,
          log: true
        }, function() {

          pomelo.request("gate.gateHandler.queryEntry", {uid: 1}, function(data) {
            console.log('switchTable dataMINH: ', data);
            const _host = data?.host ?? '';
            const _port = data?.port ?? '';
            pomelo.disconnect();
            
            pomelo.init({
              host: _host,
              port: _port,
              log: true
            }, function() {
              // call login: test, minh
              pomelo.request("connector.entryHandler.loginDevice", {deviceId: "minh5"}, function(data) {
                console.log('data: ', data);
                const userId = data?.player?.user_id ?? '';
                console.log('- userId: ', userId);
    
                // Create table
                // ----------------------------------------------------------------
                var dataPost = {
                  "smallBlind":"50",
                  "bigBlind":"100",
                  "minBuyIn":"100",
                  "maxBuyIn":"10000",
                  "minPlayers":"2",
                  "maxPlayers":"10",
                  "gameMode":"normal",
                  "zone": "CC",
                };
                // const createTable = 'game.tableHandler.createTable';
                // pomelo.request(createTable, dataPost, function(data) {
                  // console.log('createTable >> data: ', data);
                // });
    
                // Get list tables
                // pomelo.request('game.tableHandler.getTables', '', function(data) {
                  // console.log('getTables >> data: ', data);
                // });
    
                // Get list tables by zone = TS
                pomelo.request('game.tableHandler.getTables', {zone: 'TS'}, function(data) {
                  // console.log('getTables with zone = TS >> data: ', data.tables);
                  const tables = data?.tables?.tables ?? [];
                  console.log('getTables with zone = TS >> tables: ', tables);
                  if (tables.length > 0) {
                    // const randomTable = tables[Math.floor(Math.random() * tables.length)];
                    const randomTable = tables[0];
                    console.log('Get first table: ', randomTable);
                    
                    // join table
                    const tid = randomTable?.id ?? null;
                    const minBuyIn = randomTable?.minBuyIn ?? 0;
                    const maxBuyIn = randomTable?.maxBuyIn ?? 0;
    
                    const cmdJoinTable = 'game.tableHandler.joinTable';
                    pomelo.request(cmdJoinTable, {tid: tid}, function(data) {
                      console.log('joinTable >> data: ', data);
    
                      // joinGame: ngồi xuống bàn
                      const cmdJoinGame = 'game.tableHandler.joinGame';
                      const buyIn = maxBuyIn - minBuyIn;
    
                      console.log('-> maxBuyIn: ', maxBuyIn);
                      console.log('-> minBuyIn: ', minBuyIn);
                      console.log('-> buyIn: ', buyIn);
                      pomelo.request(cmdJoinGame, {buyIn: buyIn, index: 1}, function(data) {
                        console.log('joinGame >> data: ', data);
                        if (Number(data.code) === 200) {
                          // Command đứng dậy
                          // =================================================================
                          const cmdStandUp = 'game.tableHandler.standUp';
                          pomelo.request(cmdStandUp, '', function(data) {
                            console.log('standUp >> data: ', data);
                          });
                          // =================================================================
    
                          // Command switchTable
                          // =================================================================
                          const cmdSwitchTable = 'game.tableHandler.switchTable';
                          pomelo.request(cmdSwitchTable, '', function(data) {
                            console.log('switchTable >> data: ', data);
                          });
                          // =================================================================
    
                          // Command Get Thông tin 1 user
                          // =================================================================
                          /*
                          const cmdGetUserInfo = 'game.userHandler.getUserInfo';
                          pomelo.request(cmdGetUserInfo, {uid: userId}, function(data) {
                            console.log('- getUserInfo >> data: ', data);
                          });
                          */
                          // =================================================================
    
                          // Command: Get danh sách users đang ở lobby để mời chời
                          // =================================================================
                          /*
                          const cmdGetUsersInLobby = 'game.tableHandler.getUsersInLobby';
                          pomelo.request(cmdGetUsersInLobby, {uid: userId}, function(data) {
                            console.log('- getUsersInLobby >> data: ', data);
                          });
                          */
                          // =================================================================
    
                          // Thoát khỏi phòng
                          // =================================================================
                          /*
                          const cmdLeaveTable = 'game.tableHandler.leaveTable';
                          pomelo.request(cmdLeaveTable, '', function(data) {
                            console.log('leaveTable >> data: ', data);
                            
                            if (Number(data.code) === 200) {
                              console.log('Leave table successfully => chuẩn bị logout');
                              // send command: Logout
                              // =================================================================
                              const cmdLogout = 'connector.entryHandler.logout';
                              console.log('Logout with userId: ', userId);
                              pomelo.request(cmdLogout, {uid: userId}, function(dt) {
                                console.log('logout >> data: ', dt);
                              });
                              // =================================================================
                            }
                            
                          });
                          */
                          // =================================================================
                        }
                      });
    
                    });
    
                  } else {
                    console.log('No tables available');
                  }
                });
    
                // Get list rooms
                // const cmdListRooms = 'game.tableHandler.getRooms';
                // pomelo.request(cmdListRooms, '', function(data) {
                //  console.log('getRooms >> data: ', data);
                // });
                
    
    
              });

            });
          });
        });
      } // end switchTable          

      function findTable() {
        console.log("start pomelo");
        pomelo.init({
          host: host,
          port: port,
          log: true
        }, function() {
          // call login: test, minh
          pomelo.request("connector.entryHandler.loginDevice", {deviceId: "minh5"}, function(data) {
            console.log('data: ', data);
            const userId = data?.player?.user_id ?? '';
            console.log('- userId: ', userId);

            // Get find table
            // --------------------------------------------------------------------------------
            const dataFindPost = {
              "smallBlind":"50",
              "bigBlind":"100",
              "minBuyIn":"100",
              "maxBuyIn":"10000",
              "maxPlayers":"10",
              "gameMode":"normal",
              "zone": "CC",
            };
            
            pomelo.request('game.tableHandler.findTable', dataFindPost, function(data) {
              console.log('- findTable >> data: ', data);
            });
            // End: Find Table ----------------------------------------------------------------
          });
        });
      } // end findTable

      function chatInTable() {
        console.log("start pomelo");
        pomelo.init({
          host: host,
          port: port,
          log: true
        }, function() {
          // call login: test, minh
          pomelo.request("connector.entryHandler.loginDevice", {deviceId: "minh"}, function(data) {
            console.log('data: ', data);
            const userId = data?.player?.user_id ?? '';
            console.log('- userId: ', userId);

            // Get list table
            // --------------------------------------------------------------------------------
            // Get list tables by zone = TS
            pomelo.request('game.tableHandler.getTables', {zone: 'TS'}, function(data) {

              const tables = data?.tables?.tables ?? [];
              console.log('getTables with zone = TS >> tables: ', tables);
              if (tables.length > 0) {
                // const randomTable = tables[Math.floor(Math.random() * tables.length)];
                const randomTable = tables[0];
                console.log('First table: ', randomTable);
                
                // joinTable
                // ----------------------------------------------------------------------------------
                const tid = randomTable?.id ?? null;
                const minBuyIn = randomTable?.minBuyIn ?? 0;
                const maxBuyIn = randomTable?.maxBuyIn ?? 0;
                const cmdJoinTable = 'game.tableHandler.joinTable';
                pomelo.request(cmdJoinTable, {tid: tid}, function(data) {
                  console.log(' - joinTable >> data: ', data);

                  // joinGame: ngồi xuống bàn
                  // --------------------------------------------------------------------------------
                  const cmdJoinGame = 'game.tableHandler.joinGame';
                  const buyIn = maxBuyIn - minBuyIn;

                  console.log('-> maxBuyIn: ', maxBuyIn);
                  console.log('-> minBuyIn: ', minBuyIn);
                  console.log('-> buyIn: ', buyIn);
                  pomelo.request(cmdJoinGame, {buyIn: buyIn, index: 1}, function(data) {
                    console.log('- joinGame >> data: ', data);
                    if (Number(data.code) === 200) {
                      // Command: sendChat
                      const cmdSendChat = 'chat.chatHandler.sendMessage';
                      const message = 'Hello, I am Minh';
                      pomelo.request(cmdSendChat, {content: message, target  : "table"}, function(data) {
                        console.log('- sendChat >> data: ', data);
                      });
                    }
                  });
                  // End: joinGame ------------------------------------------------------------------
                });
                // End: joinTable -------------------------------------------------------------------
              } else {
                console.log('No tables available');
              }
            });
            // End: list Table ----------------------------------------------------------------
          });
        });
      } // end findTable

      function playerInGame(deviceIdName, positionIndex) {
        console.log("start pomelo");
        pomelo.init({
          host: host,
          port: port,
          log: true
        }, function() {

          pomelo.request("gate.gateHandler.queryEntry", {uid: 1}, function(data) {

            console.log('dataMINH: ', data);
            const _host = data?.host ?? '';
            const _port = data?.port ?? '';
            pomelo.disconnect();

            pomelo.init({
              host: _host,
              port: _port,
              log: true
            }, function() {

              // call login: test, minh
              pomelo.request("connector.entryHandler.loginDevice", {deviceId: deviceIdName}, function(data) {
                console.log('data player: ', data);
                const userId = data?.player?.user_id ?? '';
                console.log('- userId: ', userId);

                // Create table
                // ----------------------------------------------------------------
                var dataPost = {
                  "smallBlind":"50",
                  "bigBlind":"100",
                  "minBuyIn":"100",
                  "maxBuyIn":"10000",
                  "minPlayers":"2",
                  "maxPlayers":"10",
                  "gameMode":"normal",
                  "zone": "CC",
                };
                // const createTable = 'game.tableHandler.createTable';
                // pomelo.request(createTable, dataPost, function(data) {
                  // console.log('createTable >> data: ', data);
                // });

                // Get list tables
                // pomelo.request('game.tableHandler.getTables', '', function(data) {
                  // console.log('getTables >> data: ', data);
                // });

                // Get list tables by zone = TS
                pomelo.request('game.tableHandler.getTables', {zone: 'TS'}, function(data) {
                  // console.log('getTables with zone = TS >> data: ', data.tables);
                  const tables = data?.tables?.tables ?? [];
                  console.log('getTables with zone = TS >> tables: ', tables);
                  if (tables.length > 0) {
                    // const randomTable = tables[Math.floor(Math.random() * tables.length)];
                    const randomTable = tables[0];
                    console.log('Random table: ', randomTable);
                    
                    // join table
                    // const tid = "55f6e210-e7d8-11ef-a01a-19400bd09aa1"; // randomTable?.id ?? null;
                    const tid = randomTable?.id ?? null;
                    const minBuyIn = randomTable?.minBuyIn ?? 0;
                    const maxBuyIn = randomTable?.maxBuyIn ?? 0;

                    const cmdJoinTable = 'game.tableHandler.joinTable';
                    pomelo.request(cmdJoinTable, {tid: tid}, function(data) {
                      console.log('joinTable >> data: ', data);

                      // joinGame: ngồi xuống bàn
                      const cmdJoinGame = 'game.tableHandler.joinGame';
                      const buyIn = maxBuyIn - minBuyIn;

                      console.log('-> maxBuyIn: ', maxBuyIn);
                      console.log('-> minBuyIn: ', minBuyIn);
                      console.log('-> buyIn: ', buyIn);
                      pomelo.request(cmdJoinGame, {buyIn: buyIn, index: positionIndex}, function(data) {
                        console.log('joinGame >> data: ', data);
                        if (Number(data.code) === 200) {
                          // Command đứng dậy
                          // =================================================================
                          // const cmdStandUp = 'game.tableHandler.standUp';
                          // pomelo.request(cmdStandUp, '', function(data) {
                            // console.log('standUp >> data: ', data);
                          // });
                          // =================================================================

                          // Command Get Thông tin 1 user
                          // =================================================================
                          /*
                          const cmdGetUserInfo = 'game.userHandler.getUserInfo';
                          pomelo.request(cmdGetUserInfo, {uid: userId}, function(data) {
                            console.log('- getUserInfo >> data: ', data);
                          });
                          */
                          // =================================================================

                          // Command: Get danh sách users đang ở lobby để mời chời
                          // =================================================================
                          // const cmdGetUsersInLobby = 'game.tableHandler.getUsersInLobby';
                          // pomelo.request(cmdGetUsersInLobby, {uid: userId}, function(data) {
                            // console.log('- getUsersInLobby >> data: ', data);
                          // });
                          // =================================================================
                        }
                      });

                    });

                  } else {
                    console.log('No tables available');
                  }
                });

                // Get list rooms
                // const cmdListRooms = 'game.tableHandler.getRooms';
                // pomelo.request(cmdListRooms, '', function(data) {
                //  console.log('getRooms >> data: ', data);
                // });
                
              });
            
            });

          });

        });
      } // end playerInGame

      function playerInGame2(deviceIdName, positionIndex) {
        console.log("start pomelo");
        pomelo.init({
          host: host,
          port: port,
          log: true
        }, function() {

          pomelo.request("gate.gateHandler.queryEntry", {uid: 1}, function(data) {

            console.log('dataMINH: ', data);
            const _host = data?.host ?? '';
            const _port = data?.port ?? '';
            pomelo.disconnect();

            pomelo.init({
              host: _host,
              port: _port,
              log: true
            }, function() {

              // call login: test, minh
              pomelo.request("connector.entryHandler.loginDevice", {deviceId: deviceIdName}, function(data) {
                console.log('data player: ', data);
                const userId = data?.player?.user_id ?? '';
                console.log('- userId: ', userId);

                // Create table
                // ----------------------------------------------------------------
                var dataPost = {
                  "smallBlind":"50",
                  "bigBlind":"100",
                  "minBuyIn":"100",
                  "maxBuyIn":"10000",
                  "minPlayers":"2",
                  "maxPlayers":"10",
                  "gameMode":"normal",
                  "zone": "CC",
                };
                // const createTable = 'game.tableHandler.createTable';
                // pomelo.request(createTable, dataPost, function(data) {
                  // console.log('createTable >> data: ', data);
                // });

                // Get list tables
                // pomelo.request('game.tableHandler.getTables', '', function(data) {
                  // console.log('getTables >> data: ', data);
                // });

                // Get list tables by zone = TS
                pomelo.request('game.tableHandler.getTables', {zone: 'TS'}, function(data) {
                  // console.log('getTables with zone = TS >> data: ', data.tables);
                  const tables = data?.tables?.tables ?? [];
                  console.log('getTables with zone = TS >> tables: ', tables);
                  if (tables.length > 0) {
                    // const randomTable = tables[Math.floor(Math.random() * tables.length)];
                    const randomTable = tables[0];
                    console.log('Random table: ', randomTable);
                    
                    // join table
                    const tid = "55f6e210-e7d8-11ef-a01a-19400bd09aa1"; // randomTable?.id ?? null;
                    const minBuyIn = randomTable?.minBuyIn ?? 0;
                    const maxBuyIn = randomTable?.maxBuyIn ?? 0;

                    const cmdJoinTable = 'game.tableHandler.joinTable';
                    pomelo.request(cmdJoinTable, {tid: tid}, function(data) {
                      console.log('joinTable >> data: ', data);

                      // joinGame: ngồi xuống bàn
                      const cmdJoinGame = 'game.tableHandler.joinGame';
                      const buyIn = maxBuyIn - minBuyIn;

                      console.log('-> maxBuyIn: ', maxBuyIn);
                      console.log('-> minBuyIn: ', minBuyIn);
                      console.log('-> buyIn: ', buyIn);
                      pomelo.request(cmdJoinGame, {buyIn: buyIn, index: positionIndex}, function(data) {
                        console.log('joinGame >> data: ', data);
                        if (Number(data.code) === 200) {
                          // Command đứng dậy
                          // =================================================================
                          const cmdStandUp = 'game.tableHandler.standUp';
                          pomelo.request(cmdStandUp, '', function(data) {
                            console.log('standUp >> data: ', data);
                          });
                          // =================================================================

                          // Command Get Thông tin 1 user
                          // =================================================================
                          /*
                          const cmdGetUserInfo = 'game.userHandler.getUserInfo';
                          pomelo.request(cmdGetUserInfo, {uid: userId}, function(data) {
                            console.log('- getUserInfo >> data: ', data);
                          });
                          */
                          // =================================================================

                          // Command: Get danh sách users đang ở lobby để mời chời
                          // =================================================================
                          // const cmdGetUsersInLobby = 'game.tableHandler.getUsersInLobby';
                          // pomelo.request(cmdGetUsersInLobby, {uid: userId}, function(data) {
                            // console.log('- getUsersInLobby >> data: ', data);
                          // });
                          // =================================================================
                        }
                      });

                    });

                  } else {
                    console.log('No tables available');
                  }
                });

                // Get list rooms
                // const cmdListRooms = 'game.tableHandler.getRooms';
                // pomelo.request(cmdListRooms, '', function(data) {
                //  console.log('getRooms >> data: ', data);
                // });
                
              });
            
            });

          });

        });
      } // end playerInGame2

      function loginFacebook(deviceIdName, positionIndex) {
        console.log("start pomelo init loginFacebook");
        pomelo.init({
          host: host,
          port: port,
          log: true
        }, function() {

          pomelo.request("gate.gateHandler.queryEntry", {uid: 1}, function(data) {
            console.log('dataMINH: ', data);
            const _host = data?.host ?? '';
            const _port = data?.port ?? '';
            pomelo.disconnect();
            
            pomelo.init({
              host: _host,
              port: _port,
              log: true
            }, function() {
                console.log("start pomelo login facebook");

                // call login: test, minh
                const dataPostLoginFb = {
                  "accessToken": "EAAHkd00jAT4BO0t0sh4fKDxc39bgAGlZCyODWSMkaVGdOoCeWEXEOGnc57PyxPb72ZCyn8ziTf0yhvPrZCYHZCsZAE3zmnjWZCat5VEazx1hBxEumJCioRtGnhXrv0pLGPeiel6fLilaaYO8f6ZB9boZBr0qGDSQLn3lLlZBXdmpJb3OLB6yd7ZAML1ABNLE4LZAyz98mx1TpPA7n1EFtry6QZDZD",
                  // "facebookId": "3158302040978973",
                  "userInfo": {
                    "id": "3158302040978973",
                    "name": "Bé Tập Code",
                    "email": "<EMAIL>",
                  }
                }
                pomelo.request("connector.entryHandler.loginFacebook", dataPostLoginFb, function(data) {
                  console.log('data login facebook: ', data);
                  // const userId = data?.player?.user_id ?? '';
                  // console.log('- userId: ', userId);
                });

            });
            

          });
          
        });
      } // end loginFacebook

      function loginByEmail() {
        console.log("start pomelo init loginByEmail");
        pomelo.init({
          host: host,
          port: port,
          log: true
        }, function() {

          pomelo.request("gate.gateHandler.queryEntry", {uid: 1}, function(data) {
            console.log('dataMINH: ', data);
            const _host = data?.host ?? '';
            const _port = data?.port ?? '';
            pomelo.disconnect();
            
            pomelo.init({
              host: _host,
              port: _port,
              log: true
            }, function() {
                console.log("start pomelo login by email");
                const dataByEmail = {
                  "email": "<EMAIL>",
                  "password": "p@kee123"
                }
                pomelo.request("connector.entryHandler.loginByEmail", dataByEmail, function(data) {
                  console.log('> data login by email: ', data);
                  // const userId = data?.player?.user_id ?? '';
                  // console.log('- userId: ', userId);
                });

            });
            

          });
          
        });
      } // end loginByEmail      

      function loginAndGetUserInfo(deviceIdName) {
        console.log("start pomelo");
        pomelo.init({
          host: host,
          port: port,
          log: true
        }, function() {

          pomelo.request("gate.gateHandler.queryEntry", {uid: 1}, function(data) {

            console.log('dataMINH: ', data);
            const _host = data?.host ?? '';
            const _port = data?.port ?? '';
            pomelo.disconnect();

            pomelo.init({
              host: _host,
              port: _port,
              log: true
            }, function() {

              // call login: test, minh
              pomelo.request("connector.entryHandler.loginDevice", {deviceId: deviceIdName}, function(data) {
                console.log('data player: ', data);
                // const userId = data?.player?.user_id ?? '';
                const userId = data?.player?.id ?? 0;
                console.log('- userId: ', userId);

                // Create table
                // ----------------------------------------------------------------
                // const createTable = 'game.tableHandler.createTable';
                // pomelo.request(createTable, dataPost, function(data) {
                  // console.log('createTable >> data: ', data);
                // });

                // Command Get Thông tin 1 user
                // =================================================================
                
                const cmdGetUserInfo = 'game.userHandler.getUserInfo';
                pomelo.request(cmdGetUserInfo, {uid: userId}, function(data) {
                  console.log('- getUserInfo >> data: ', data);
                });
                
              });
            
            });

          });

        });
      } // end player

      

      function toggleAutoSpin(deviceIdName) {
        console.log("start spinMiniGame");
        pomelo.init({
          host: host,
          port: port,
          log: true
        }, function() {
          
          pomelo.request("gate.gateHandler.queryEntry", {uid: 1}, function(data) {
            console.log('data gate: ', data);
            const _host = data?.host ?? '';
            const _port = data?.port ?? '';
            pomelo.disconnect();

            pomelo.init({
              host: _host,
              port: _port,
              log: true
            }, function() {
              pomelo.request("connector.entryHandler.loginDevice", {deviceId: deviceIdName}, function(data) {
                console.log('data after login: ', data);
                const userId = data?.player?.user_id ?? '';
                console.log('- userId: ', userId);
    
                // bật tắt chế độ tự động quay
                const cmdToggleAutoSpin = 'game.minigameHandler.toggleAutoSpin';
                pomelo.request(cmdToggleAutoSpin, {autoSpin: true, betAmount: 1000000}, function(data) {
                  console.log('toggleAutoSpin >> data: ', data);
                });           
    
              });
            });
          });
        });
      } // end toggleAutoSpin - bật tắt chế độ tự động quay
      
      function chat1to1(deviceIdName, uidFriend) {
        console.log("start pomelo");
        pomelo.init({
          host: host,
          port: port,
          log: true
        }, function() {

          pomelo.request("gate.gateHandler.queryEntry", {uid: 1}, function(data) {

            console.log('chat1to1 >> data: ', data);
            const _host = data?.host ?? '';
            const _port = data?.port ?? '';
            pomelo.disconnect();

            pomelo.init({
              host: _host,
              port: _port,
              log: true
            }, function() {

              // call login: test, minh
              pomelo.request("connector.entryHandler.loginDevice", {deviceId: deviceIdName}, function(data) {
                console.log(deviceIdName, ' -> data player: ', data);
                const userId = data?.player?.user_id ?? '';
                console.log('- userId: ', userId);

                // Sleep 3s sau đó tạo ra ngẫu nhiên 1 đoạn nội dung chat và tiến hành gửi tin nhắn đi, lặp qua việc này 10 lần
                for (let i = 0; i < 10; i++) {
                  setTimeout(() => {
                    const messages = [
                      'Hi there!',
                      'How are you doing?', 
                      'Nice to meet you!',
                      'What\'s up?',
                      'Having a good day?',
                      'Hello friend!',
                      'Hey, how\'s it going?',
                      'Greetings!',
                      'Good to see you!',
                      'Hi, hope you\'re well!'
                    ];
                    const content = messages[Math.floor(Math.random() * messages.length)];
                    const cmdChat1to1 = 'chat.chatHandler.sendMessage';
                    pomelo.request(cmdChat1to1, {target: uidFriend, content: content}, function(data) {
                      console.log('chat1to1 ' + i + ' >> data: ', data);
                    });
                  }, 3000);
                }
                
                
                // send message to deviceIdName2
                /* const cmdChat1to1 = 'chat.chatHandler.sendMessage';
                pomelo.request(cmdChat1to1, {target: uidFriend, content: 'Hello, how are you?'}, function(data) {
                  console.log('chat1to1 >> data: ', data);
                }); */

                
              });
            
            });

          });

        });
      } // end chat1to1

      function getChatSessions(deviceIdName, uid) {
        console.log("start pomelo");
        pomelo.init({
          host: host,
          port: port,
          log: true
        }, function() {

          pomelo.request("gate.gateHandler.queryEntry", {uid: 1}, function(data) {

            console.log('chat1to1 >> data: ', data);
            const _host = data?.host ?? '';
            const _port = data?.port ?? '';
            pomelo.disconnect();

            pomelo.init({
              host: _host,
              port: _port,
              log: true
            }, function() {

              // call login: test, minh
              pomelo.request("connector.entryHandler.loginDevice", {deviceId: deviceIdName}, function(data) {
                console.log(deviceIdName, ' -> data player: ', data);
                const userId = data?.player?.user_id ?? '';
                console.log('- userId: ', userId);

                const cmdGetChatSessions = 'chat.chatHandler.getChatSessions';
                pomelo.request(cmdGetChatSessions, {uid: uid}, function(data) {
                  console.log('getChatSessions >> data: ', data);
                });
                
              });
            
            });

          });

        });
      } // end getChatSessions

      function getChatHistories(deviceIdName, uid) {
        console.log("start pomelo");
        pomelo.init({
          host: host,
          port: port,
          log: true
        }, function() {

          pomelo.request("gate.gateHandler.queryEntry", {uid: 1}, function(data) {

            console.log('chat1to1 >> data: ', data);
            const _host = data?.host ?? '';
            const _port = data?.port ?? '';
            pomelo.disconnect();

            pomelo.init({
              host: _host,
              port: _port,
              log: true
            }, function() {

              // call login: test, minh
              pomelo.request("connector.entryHandler.loginDevice", {deviceId: deviceIdName}, function(data) {
                console.log(deviceIdName, ' -> data player: ', data);
                const userId = data?.player?.user_id ?? '';
                console.log('- userId: ', userId);

                const cmdGetChatHistories = 'chat.chatHandler.getChatHistories';
                pomelo.request(cmdGetChatHistories, {target_id: uid, page: 1, pageSize: 20}, function(data) {
                  console.log('getChatHistories >> data: ', data);
                });
                
              });
            
            });

          });

        });
      } // end getChatHistories

      function getUnreadMessageCount(deviceIdName, uid) {
        console.log("start pomelo");
        pomelo.init({
          host: host,
          port: port,
          log: true
        }, function() {

          pomelo.request("gate.gateHandler.queryEntry", {uid: 1}, function(data) {

            console.log('chat1to1 >> data: ', data);
            const _host = data?.host ?? '';
            const _port = data?.port ?? '';
            pomelo.disconnect();

            pomelo.init({
              host: _host,
              port: _port,
              log: true
            }, function() {

              // call login: test, minh
              pomelo.request("connector.entryHandler.loginDevice", {deviceId: deviceIdName}, function(data) {
                console.log(deviceIdName, ' -> data player: ', data);
                const userId = data?.player?.user_id ?? '';
                console.log('- userId: ', userId);

                const cmdGetUnreadMessageCount = 'chat.chatHandler.getUnreadMessageCount';
                pomelo.request(cmdGetUnreadMessageCount, {uid: uid}, function(data) {
                  console.log('getUnreadMessageCount >> data: ', data);
                });
                
              });
            
            });

          });

        });
      } // end getUnreadMessageCount

      function markAllMessagesAsRead(deviceIdName, uid) {
        console.log("start pomelo");
        pomelo.init({
          host: host,
          port: port,
          log: true
        }, function() {

          pomelo.request("gate.gateHandler.queryEntry", {uid: 1}, function(data) {

            console.log('chat1to1 >> data: ', data);
            const _host = data?.host ?? '';
            const _port = data?.port ?? '';
            pomelo.disconnect();

            pomelo.init({
              host: _host,
              port: _port,
              log: true
            }, function() {

              // call login: test, minh
              pomelo.request("connector.entryHandler.loginDevice", {deviceId: deviceIdName}, function(data) {
                console.log(deviceIdName, ' -> data player: ', data);
                const userId = data?.player?.user_id ?? '';
                console.log('- userId: ', userId);

                const cmdGetUnreadMessageCount = 'chat.chatHandler.markAllMessagesAsRead';
                pomelo.request(cmdGetUnreadMessageCount, {friend_id: 19}, function(data) {
                  console.log('getUnreadMessageCount >> data: ', data);
                });
                
              });
            
            });

          });

        });
      } // end markAllMessagesAsRead

      function deleteMessage(deviceIdName, uid, message_id) {
        console.log("start pomelo");
        pomelo.init({
          host: host,
          port: port,
          log: true
        }, function() {

          pomelo.request("gate.gateHandler.queryEntry", {uid: 1}, function(data) {

            console.log('chat1to1 >> data: ', data);
            const _host = data?.host ?? '';
            const _port = data?.port ?? '';
            pomelo.disconnect();

            pomelo.init({
              host: _host,
              port: _port,
              log: true
            }, function() {

              // call login: test, minh
              pomelo.request("connector.entryHandler.loginDevice", {deviceId: deviceIdName}, function(data) {
                console.log(deviceIdName, ' -> data player: ', data);
                const userId = data?.player?.user_id ?? '';
                console.log('- userId: ', userId);

                const cmdDeleteMessage = 'chat.chatHandler.deleteMessage';
                pomelo.request(cmdDeleteMessage, {message_id: message_id}, function(data) {
                  console.log('deleteMessage >> data: ', data);
                });
                
              });
            
            });

          });

        });
      } // end deleteMessage

      function getAllBadges(deviceName = 'minh5') {
        console.log("getAllBadges: ", deviceName);
        pomelo.init({
          host: host,
          port: port,
          log: true
        }, function() {
          pomelo.request("gate.gateHandler.queryEntry", {uid: 1}, function(data) {
            const _host = data?.host ?? '';
            const _port = data?.port ?? '';
            pomelo.disconnect();
            
            pomelo.init({
              host: _host,
              port: _port,
              log: true
            }, function() {
              // Login first
              pomelo.request("connector.entryHandler.loginDevice", {deviceId: deviceName}, function(data) {
                console.log('Login data:', data);
                
                // Send notification to all tables in zone TS
                const dataPost = {
                  // type: "POKE_CAREER" // POKE_CAREER, ACHIEVEMENTS
                  type: ["POKE_CAREER", "ACHIEVEMENTS"]
                };
                
                pomelo.request('game.userHandler.getAllBadges', dataPost, function(response) {
                  console.log('getAllBadges response:', response);
                });
              });
            });
          });
        });
      } // end getAllBadges

      function claimBadgeReward(deviceName = 'minh5', badge_id = 6) {
        console.log("getAllBadges: ", deviceName);
        pomelo.init({
          host: host,
          port: port,
          log: true
        }, function() {
          pomelo.request("gate.gateHandler.queryEntry", {uid: 1}, function(data) {
            const _host = data?.host ?? '';
            const _port = data?.port ?? '';
            pomelo.disconnect();
            
            pomelo.init({
              host: _host,
              port: _port,
              log: true
            }, function() {
              // Login first
              pomelo.request("connector.entryHandler.loginDevice", {deviceId: deviceName}, function(data) {
                console.log('Login data:', data);
                
                // Send notification to all tables in zone TS
                const dataPost = {
                  badge_id: badge_id,
                };
                
                pomelo.request('game.userHandler.claimBadgeReward', dataPost, function(response) {
                  console.log('getAllBadges response:', response);
                });
              });
            });
          });
        });
      } // end getAllBadges

      function getShopItems(deviceName = 'minh5') {
        console.log("getShopItems: ", deviceName);
        pomelo.init({
          host: host,
          port: port,
          log: true
        }, function() {
          pomelo.request("gate.gateHandler.queryEntry", {uid: 1}, function(data) {
            const _host = data?.host ?? '';
            const _port = data?.port ?? '';
            pomelo.disconnect();
            
            pomelo.init({
              host: _host,
              port: _port,
              log: true
            }, function() {
              // Login first
              pomelo.request("connector.entryHandler.loginDevice", {deviceId: deviceName}, function(data) {
                console.log('Login data:', data);
                
                const dataPost = {
                  grouped: true
                };
                
                pomelo.request('game.shopHandler.getShopItems', dataPost, function(response) {
                  console.log('getShopItems >> response:', response);
                });
              });
            });
          });
        });
      } // end getShopItems

      function getShopItemDetail(deviceName = 'minh5', itemId) {
        console.log("getShopItemDetail: ", deviceName);
        pomelo.init({
          host: host,
          port: port,
          log: true
        }, function() {
          pomelo.request("gate.gateHandler.queryEntry", {uid: 1}, function(data) {
            const _host = data?.host ?? '';
            const _port = data?.port ?? '';
            pomelo.disconnect();
            
            pomelo.init({
              host: _host,
              port: _port,
              log: true
            }, function() {
              // Login first
              pomelo.request("connector.entryHandler.loginDevice", {deviceId: deviceName}, function(data) {
                console.log('Login data:', data);
                
                const dataPost = {
                  item_id: itemId,
                };
                
                pomelo.request('game.shopHandler.getShopItemDetail', dataPost, function(response) {
                  console.log('getShopItemDetail >> response:', response);
                });
              });
            });
          });
        });
      } // end getShopItemDetail

      function buyItem(deviceName = 'minh5', itemId) {
        console.log("buyItem: ", deviceName);
        pomelo.init({
          host: host,
          port: port,
          log: true
        }, function() {
          pomelo.request("gate.gateHandler.queryEntry", {uid: 1}, function(data) {
            const _host = data?.host ?? '';
            const _port = data?.port ?? '';
            pomelo.disconnect();
            
            pomelo.init({
              host: _host,
              port: _port,
              log: true
            }, function() {
              // Login first
              pomelo.request("connector.entryHandler.loginDevice", {deviceId: deviceName}, function(data) {
                console.log('Login data:', data);
                
                // Send notification to all tables in zone TS
                const dataPost = {
                  item_id: 1,
                  platform: "web"
                };
                
                pomelo.request('game.shopHandler.buyItem', dataPost, function(response) {
                  console.log('buyItem >> response:', response);
                });
              });
            });
          });
        });
      } // end buyItem

      function getPurchaseHistory(deviceName = 'minh5') {
        console.log("getPurchaseHistory: ", deviceName);
        pomelo.init({
          host: host,
          port: port,
          log: true
        }, function() {
          pomelo.request("gate.gateHandler.queryEntry", {uid: 1}, function(data) {
            const _host = data?.host ?? '';
            const _port = data?.port ?? '';
            pomelo.disconnect();
            
            pomelo.init({
              host: _host,
              port: _port,
              log: true
            }, function() {
              // Login first
              pomelo.request("connector.entryHandler.loginDevice", {deviceId: deviceName}, function(data) {
                console.log('Login data:', data);
                
                pomelo.request('game.shopHandler.getPurchaseHistory', {}, function(response) {
                  console.log('getPurchaseHistory >> response:', response);
                });
              });
            });
          });
        });
      } // end getPurchaseHistory

      function getMissions(deviceName = 'minh5') {
        console.log("getShopItems: ", deviceName);
        pomelo.init({
          host: host,
          port: port,
          log: true
        }, function() {
          pomelo.request("gate.gateHandler.queryEntry", {uid: 1}, function(data) {
            const _host = data?.host ?? '';
            const _port = data?.port ?? '';
            pomelo.disconnect();
            
            pomelo.init({
              host: _host,
              port: _port,
              log: true
            }, function() {
              // Login first
              pomelo.request("connector.entryHandler.loginDevice", {deviceId: deviceName}, function(data) {
                console.log('Login data:', data);
                
                pomelo.request('game.missionsHandler.getMissions', {}, function(response) {
                  console.log('getMissions >> response:', response);
                });
              });
            });
          });
        });
      } // end getMissions

      function claimRewardMission(deviceName = 'minh5', missionId) {
        console.log("claimRewardMission: ", deviceName);
        pomelo.init({
          host: host,
          port: port,
          log: true
        }, function() {
          pomelo.request("gate.gateHandler.queryEntry", {uid: 1}, function(data) {
            const _host = data?.host ?? '';
            const _port = data?.port ?? '';
            pomelo.disconnect();
            
            pomelo.init({
              host: _host,
              port: _port,
              log: true
            }, function() {
              // Login first
              pomelo.request("connector.entryHandler.loginDevice", {deviceId: deviceName}, function(data) {
                console.log('Login data:', data);
                
                pomelo.request('game.missionsHandler.claimReward', {mission_id: missionId}, function(response) {
                  console.log('claimReward >> response:', response);
                });
              });
            });
          });
        });
      } // end claimRewardMission

      function getTournaments(deviceName = 'minh5') {
        console.log("getTournaments: ", deviceName);
        pomelo.init({
          host: host,
          port: port,
          log: true
        }, function() {
          pomelo.request("gate.gateHandler.queryEntry", {uid: 1}, function(data) {
            const _host = data?.host ?? '';
            const _port = data?.port ?? '';
            pomelo.disconnect();
            
            pomelo.init({
              host: _host,
              port: _port,
              log: true
            }, function() {
              // Login first
              pomelo.request("connector.entryHandler.loginDevice", {deviceId: deviceName}, function(data) {
                console.log('Login data:', data);
                
                pomelo.request('game.sngTableHandler.getTournaments', {}, function(response) {
                  console.log('getTournaments >> response:', response);
                  displayTournaments(response, deviceName);
                });
              });
            });
          });
        });
      } // end getTournaments

      function getTournamentsWithSelectedAccount() {
        const selectedAccount = document.getElementById('accountSelect').value;
        console.log('Selected account:', selectedAccount);
        getTournaments(selectedAccount);
      }

      function displayTournaments(response, accountName = null) {
        const tournamentList = document.getElementById('tournamentList');
        const selectedAccount = accountName || document.getElementById('accountSelect').value;
        
        if (!response || !response.tournaments) {
          tournamentList.innerHTML = '<p>No tournaments available</p>';
          return;
        }

        // Group tournaments by table_type
        const groupedTournaments = {};
        response.tournaments.forEach(tournament => {
          if (!groupedTournaments[tournament.table_type]) {
            groupedTournaments[tournament.table_type] = [];
          }
          groupedTournaments[tournament.table_type].push(tournament);
        });

        let html = '';
        Object.keys(groupedTournaments).forEach(tableType => {
          const tournaments = groupedTournaments[tableType];
          const tableTypeName = tableType.replace('_', ' ') + ' Players';
          
          html += `
            <div style="margin-bottom: 30px; padding: 15px; border: 2px solid #007bff; border-radius: 8px; background-color: white;">
              <h4 style="color: #007bff; margin-bottom: 15px;">${tableTypeName}</h4>
              <div style="display: grid; grid-template-columns: repeat(auto-fit, minmax(350px, 1fr)); gap: 15px;">
          `;
          
          tournaments.forEach(tournament => {
            console.log('Tournament item:', tournament);
            const buyInFormatted = (tournament.buy_in / 1000000).toLocaleString();
            const feeFormatted = (tournament.fee / 1000000).toLocaleString();
            const prizePoolFormatted = (tournament.prize_pool / 1000000).toLocaleString();
            
            // Format payout structure
            let payoutHtml = '';
            tournament.payout_structure.forEach(payout => {
              const amountFormatted = (payout.amount / 1000000).toLocaleString();
              const icon = payout.position === 1 ? '🥇' : payout.position === 2 ? '🥈' : '🥉';
              payoutHtml += `<div>${icon} ${payout.rank}: ${amountFormatted}M (${payout.percentage}%)</div>`;
            });
            
            const statusColor = tournament.status === 'WAITING_FOR_PLAYERS' ? '#ffc107' : '#28a745';
            const statusText = tournament.status.replace('_', ' ');
            
            // Kiểm tra xem user đã đăng ký tournament này chưa
            // Sử dụng tournament_code để so sánh với registeredTournaments
            const tournamentCode = tournament.tournament_code || tournament.id;
            const tournamentId = registeredTournaments[selectedAccount] ? registeredTournaments[selectedAccount][0].tournament_id : 0;
            console.log('registeredTournaments[selectedAccount]: ', registeredTournaments[selectedAccount]);
            const isRegistered = registeredTournaments[selectedAccount] ? registeredTournaments[selectedAccount][0].tournament_code === tournamentCode : false;
            
            // Debug log
            console.log('Tournament Code:', tournamentCode, 'Tournament ID:', tournament.id);
            console.log('Registered tournaments for', selectedAccount, ':', registeredTournaments[selectedAccount]);
            console.log('Is registered:', isRegistered);
            console.log('registeredTournamentsd:', registeredTournaments, ' -> tournamentId: ', tournamentId);
            
            html += `
              <div style="padding: 12px; border: 1px solid #ddd; border-radius: 6px; background-color: #f8f9f9;">
                <h5 style="color: #28a745; margin-bottom: 10px;">${tournament.name}</h5>
                <div style="font-size: 14px; margin-bottom: 10px;">
                  <div style="background-color: #e9ecef; padding: 8px; border-radius: 4px; margin-bottom: 8px;">
                    <strong>👥 Registered Players:</strong> 
                    <span style="color: #007bff; font-weight: bold; font-size: 16px;">${tournament.registered_players}</span>
                    <span> / ${tournament.player_capacity}</span>
                  </div>
                  <div><strong>Players Needed:</strong> <span style="color: #dc3545; font-weight: bold;">${tournament.players_needed}</span></div>
                  <div><strong>Buy-in:</strong> ${buyInFormatted}M chips</div>
                  <div><strong>Fee:</strong> ${feeFormatted}M chips</div>
                  <div><strong>Prize Pool:</strong> ${prizePoolFormatted}M chips</div>
                  <div><strong>Initial Chips:</strong> ${(tournament.initial_chips / 1000000).toLocaleString()}M</div>
                  <div><strong>Blind Duration:</strong> ${tournament.blind_duration_minutes} minutes</div>
                  <div style="color: ${statusColor}; font-weight: bold;"><strong>Status:</strong> ${statusText}</div>
                  <div style="margin-top: 8px;"><strong>Payouts:</strong></div>
                  <div style="margin-left: 10px;">
                    ${payoutHtml}
                  </div>
                </div>`;
            
            // Hiển thị buttons tương ứng với trạng thái đăng ký
            if (isRegistered) {
              // Nếu đã đăng ký, chỉ hiển thị View Details và Leave Tournament
                             html += `
                 <button 
                   onclick="getTournament('${selectedAccount}', '${tournamentId}')"
                   style="background-color: #007bff; color: white; border: none; padding: 8px 16px; border-radius: 4px; cursor: pointer; font-weight: bold; margin-right: 8px;"
                   onmouseover="this.style.backgroundColor='#0056b3'"
                   onmouseout="this.style.backgroundColor='#007bff'"
                 >
                   View Details
                 </button>
                 <button 
                   onclick="leaveTournament('${selectedAccount}', '${tournamentId}')"
                   style="background-color: #dc3545; color: white; border: none; padding: 8px 16px; border-radius: 4px; cursor: pointer; font-weight: bold;"
                   onmouseover="this.style.backgroundColor='#c82333'"
                   onmouseout="this.style.backgroundColor='#dc3545'"
                 >
                   Leave Tournament
                 </button>`;
            } else {
              // Nếu chưa đăng ký, chỉ hiển thị Register button
              html += `
                <button 
                  onclick="registerTournament('${selectedAccount}', '${tournament.table_type}', '${tournament.level}')"
                  style="background-color: #28a745; color: white; border: none; padding: 8px 16px; border-radius: 4px; cursor: pointer; font-weight: bold; margin-right: 8px;"
                  onmouseover="this.style.backgroundColor='#218838'"
                  onmouseout="this.style.backgroundColor='#28a745'"
                >
                  Register ${selectedAccount}
                </button>`;
            }
            
            html += `
              </div>
            `;
          });
          
          html += `
              </div>
            </div>
          `;
        });

        tournamentList.innerHTML = html;
      }

      function getTournament(deviceName = 'minh5', tournamentId) {
        console.log("getTournament: ", deviceName, ' -> tournamentId: ', tournamentId);
        pomelo.init({
          host: host,
          port: port,
          log: true
        }, function() {
          pomelo.request("gate.gateHandler.queryEntry", {uid: 1}, function(data) {
            const _host = data?.host ?? '';
            const _port = data?.port ?? '';
            pomelo.disconnect();
            
            pomelo.init({
              host: _host,
              port: _port,
              log: true
            }, function() {
              // Login first
              pomelo.request("connector.entryHandler.loginDevice", {deviceId: deviceName}, function(data) {
                console.log('Login data:', data);
                
                pomelo.request('game.sngTableHandler.getTournament', {tournamentId: tournamentId}, function(response) {
                  console.log('getTournament >> response:', response);
                });
              });
            });
          });
        });
      } // end getTournament


      function registerTournament(deviceName = 'minh5', type, level) {
        console.log("registerTournament: ", deviceName);
        pomelo.init({
          host: host,
          port: port,
          log: true
        }, function() {
          pomelo.request("gate.gateHandler.queryEntry", {uid: 1}, function(data) {
            const _host = data?.host ?? '';
            const _port = data?.port ?? '';
            pomelo.disconnect();
            
            pomelo.init({
              host: _host,
              port: _port,
              log: true
            }, function() {
              // Login first
              pomelo.request("connector.entryHandler.loginDevice", {deviceId: deviceName}, function(data) {
                console.log('Login data:', data);
                
                pomelo.request('game.sngTableHandler.registerTournament', {table_type: type, level: level}, function(response) {
                  console.log('registerTournament >> response:', response);
                  
                  // Lưu thông tin tournament đã đăng ký thành công
                  if (response && response.code === 200 && response.tournament_code) {
                    if (!registeredTournaments[deviceName]) {
                      registeredTournaments[deviceName] = [];
                    }
                    registeredTournaments[deviceName].push(response);
                    console.log('Registered tournaments for', deviceName, ':', registeredTournaments[deviceName]);
                    
                    // Refresh danh sách tournaments để hiển thị trạng thái mới
                    getTournaments(deviceName);
                  }
                });
              });
            });
          });
        });
      } // end registerTournament

      function leaveTournament(deviceName = 'minh5', tournamentId) {
        console.log("leaveTournament: ", deviceName, ' -> tournamentId: ', tournamentId);
        pomelo.init({
          host: host,
          port: port,
          log: true
        }, function() {
          pomelo.request("gate.gateHandler.queryEntry", {uid: 1}, function(data) {
            const _host = data?.host ?? '';
            const _port = data?.port ?? '';
            pomelo.disconnect();
            
            pomelo.init({
              host: _host,
              port: _port,
              log: true
            }, function() {
              // Login first
              pomelo.request("connector.entryHandler.loginDevice", {deviceId: deviceName}, function(data) {
                console.log('Login data:', data);
                
                pomelo.request('game.sngTableHandler.leaveTournament', {tournamentId: tournamentId}, function(response) {
                  console.log('leaveTournament: ', tournamentId, ' >> response:', response);
                  
                  // Xóa tournament khỏi danh sách đã đăng ký nếu rời thành công
                  if (response && response.code === 200) {
                    if (registeredTournaments[deviceName]) {
                      const index = registeredTournaments[deviceName].indexOf(tournamentId);
                      if (index > -1) {
                        registeredTournaments[deviceName].splice(index, 1);
                        console.log('Removed tournament', tournamentId, 'from registered list for', deviceName);
                        
                        // Refresh danh sách tournaments để hiển thị trạng thái mới
                        getTournaments(deviceName);
                      }
                    }
                  }
                });
              });
            });
          });
        });
      } // end leaveTournament

      pomelo.on("onInvitePlay", function (data) {
        console.log("[gameMessageEvent]->onInvitePlay: " + JSON.stringify(data));
      });

      pomelo.on("onChat", function (data) {
        console.log("[gameMessageEvent]->onChat: " + JSON.stringify(data));
      });

      pomelo.on("onJoinTable", function (data) {
        console.log("[gameMessageEvent]->onJoinTable: " + JSON.stringify(data));
      });

      pomelo.on("onJoinGame", function (data) {
        console.log("[gameMessageEvent]->onJoinGame: " + JSON.stringify(data));
      });

      pomelo.on("onTableEvent", function (data) {
        console.log("[gameMessageEvent]->onTableEvent: " + JSON.stringify(data));
      });

      pomelo.on("onEndTurn", function (data) {
        console.log("[gameMessageEvent]->onEndTurn: " + JSON.stringify(data));
      });

      pomelo.on("onEndGame", function (data) {
        console.log("[gameMessageEvent]->onEndGame: " + JSON.stringify(data));
      });

      pomelo.on("onClearTable", function (data) {
        console.log("[gameMessageEvent]->onClearTable: " + JSON.stringify(data));
      });

      pomelo.on("onUpdateMyself", function (data) {
        console.log("[gameMessageEvent]->onUpdateMyself: " + JSON.stringify(data));
      });

      pomelo.on("onNotification", function (data) {
        console.log("[gameMessageEvent]->onNotification: " + JSON.stringify(data));
      });

      pomelo.on("onFriends", function (data) {
        console.log("[gameMessageEvent]->onFriends: " + JSON.stringify(data));
      });

      pomelo.on("onChat", function (data) {
        console.log("[onChat]->data: " + JSON.stringify(data));
      });

      pomelo.on("onUserChat", function (data) {
        console.log("[onUserChat]->data: " + JSON.stringify(data));
      });

      // SNG event
      pomelo.on("onSngTournamentList", function (data) {
        console.log("[onSngTournamentList]->data: " + JSON.stringify(data));
      });

      pomelo.on("onSngTournamentJoin", function (data) {
        console.log("[onSngTournamentJoin]->data: ", JSON.stringify(data));
      });

      pomelo.on("onSngTournamentStatus", function (data) {
        console.log("[onSngTournamentStatus]->data: " + JSON.stringify(data));
      });

      pomelo.on("onSngBlindUpdate", function (data) {
        console.log("[onSngBlindUpdate]->data: " + JSON.stringify(data));
      });

      pomelo.on("onSngTournamentResult", function (data) {
        console.log("[onSngTournamentResult]->data: " + JSON.stringify(data));
      });

      pomelo.on("onSngPlayerEliminated", function (data) {
        console.log("[onSngPlayerEliminated]->data: ", JSON.stringify(data));
      });

      pomelo.on("onSngTournamentUpdate", function (data) {
        console.log("[onSngTournamentUpdate " + new Date(new Date().getTime() + 7*60*60*1000).toISOString() + "]->data: " + JSON.stringify(data));
      });

      pomelo.on("onSngTournamentEnded", function (data) {
        console.log("[onSngTournamentEnded " + new Date(new Date().getTime() + 7*60*60*1000).toISOString() + "]->data: " + JSON.stringify(data));
      });
    
    </script>
 
  </head>
  <br>

      <div class="g-banner" style="border:none">
      </div>
      <div class="g-button">
        <input id="test" type="button" value="Test Game Server (login device)" onclick="show('minh13')" />
      </div>

      <div class="g-button">
        <input id="test" type="button" value="Get User Info" onclick="loginAndGetUserInfo('minh5')" />
      </div>

      <div class="g-button">
        <input id="test" type="button" value="Login Into Lobby (minh4)" onclick="show2()" />
      </div>

      <div class="g-button">
        <input id="test" type="button" value="switchTable" onclick="switchTable()" />
      </div>

      <div class="g-button">
        <input id="test" type="button" value="findTable" onclick="findTable()" />
      </div>

      <hr />

      <div class="g-button">
        <input id="test" type="button" value="Chat In Table" onclick="chatInTable()" />
      </div>

      <div class="g-button">
        <input id="test" type="button" value="Chat 1 to 1 (minh5)" onclick="chat1to1('minh5', 19)" />
      </div>
      
      <div class="g-button">
        <input id="test" type="button" value="Chat 1 to 1 (minh4)" onclick="chat1to1('minh4', 5)" />
      </div>

      <div class="g-button">
        <input id="test" type="button" value="Get danh sách Chat của (minh5)" onclick="getChatSessions('minh5', 5)" />
      </div>

      <div class="g-button">
        <input id="test" type="button" value="Lịch sử chat của minh5 và minh4" onclick="getChatHistories('minh5', 19)" />
      </div>

      <div class="g-button">
        <input id="test" type="button" value="Đếm số tin nhắn chưa đọc theo từng sessions" onclick="getUnreadMessageCount('minh5', 5)" />
      </div>

      <div class="g-button">
        <input id="test" type="button" value="Đánh dấu tất cả tin nhắn đã đọc" onclick="markAllMessagesAsRead('minh5', 5)" />
      </div>

      <div class="g-button">
        <input id="test" type="button" value="Xoá tin nhắn của minh5 với message_id = 4" onclick="deleteMessage('minh5', 5, 4)" />
      </div>

      <hr />

      <div class="g-button">
        <input id="test" type="button" value="Player 1 (minh5)" onclick="playerInGame('minh5', 5)" />
      </div>

      <div class="g-button">
        <input id="test" type="button" value="Player 2 (minh)" onclick="playerInGame('minh', 2)" />
      </div>

      <div class="g-button">
        <input id="test" type="button" value="Player 3 (minh3) ngồi xuống, đứng dậy" onclick="playerInGame2('minh3', 4)" />
      </div>

      <div class="g-button">
        <input id="test" type="button" value="Login with Facebook Info" onclick="loginFacebook()" />
      </div>

      <div class="g-button">
        <input id="test" type="button" value="Login By Email" onclick="loginByEmail()" />
      </div>

      <hr />
      
      
        <hr />
        <h2>Giải đấu SNG</h2>
        <div style="margin-bottom: 15px;">
          <label for="accountSelect" style="font-weight: bold; margin-right: 10px;">Chọn tài khoản:</label>
          <select id="accountSelect" style="padding: 5px; margin-right: 10px; border: 1px solid #ccc; border-radius: 4px;">
            <option value="minh">minh</option>
            <option value="minh3" selected>minh3</option>
            <option value="minh4">minh4</option>
            <option value="minh5">minh5</option>
            <option value="minh13">minh13</option>
          </select>
          <input id="test" type="button" value="Danh sách Giải đấu" onclick="getTournamentsWithSelectedAccount()" style="padding: 8px 16px; background-color: #007bff; color: white; border: none; border-radius: 4px; cursor: pointer;" />
        </div>

        <!-- Tournament Display Area -->
        <div id="tournamentDisplay" style="margin: 20px 0; padding: 20px; border: 1px solid #ccc; background-color: #f9f9f9;">
          <h3>Available Tournaments</h3>
          <div id="tournamentList">
            <!-- Tournament data will be displayed here -->
          </div>
        </div>

        <!--
        <div class="g-button">
          <input id="test" type="button" value="Xem thông tin chi tiết 1 giải đấu (minh5)" onclick="getTournament('minh3', 256)" />
        </div>
        -->

        <div class="g-button">
          <input id="test" type="button" value="Đăng ký giải đấu 5_PLAYERS - BEGINNER (minh5)" onclick="registerTournament('minh3', '5_PLAYERS', 'BEGINNER')" />
        </div>

        <div class="g-button">
          <input id="test" type="button" value="Thoát khỏi giải đấu 5_PLAYERS - BEGINNER (minh5)" onclick="leaveTournament('minh3', 256)" />
        </div>

        <br />
        <br />
        <br />

    </div>
  </body>
</html>
