/**
 * Constants for SNG tournaments
 */
module.exports = {
    // Tournament types
    TOURNAMENT_TYPE: {
        SNG_5: '5_PLAYERS',
        SNG_9: '9_PLAYERS',
        SNG_3: '3_PLAYERS' // FOR TESTING ONLY - TO BE REMOVED BEFORE PRODUCTION
    },

    // Tournament levels
    TOURNAMENT_LEVEL: {
        BEGINNER: 'BEGINNER',
        INTERMEDIATE: 'INTERMEDIATE',
        ADVANCED: 'ADVANCED',
        PRO: 'PRO',
        TEST: 'TEST' // FOR TESTING ONLY - TO BE REMOVED BEFORE PRODUCTION
    },

    // Tournament status
    TOURNAMENT_STATUS: {
        WAITING: 'WAITING',
        READY: 'READY',
        IN_PROGRESS: 'IN_PROGRESS',
        ENDED: 'ENDED',
        SYSTEM: 'SYSTEM'
    },

    // Player status in tournament
    PLAYER_STATUS: {
        ACTIVE: 'ACTIVE',
        ELIMINATED: 'ELIMINATED',
        WINNER: 'WINNER'
    },

    // Default tournament settings
    DEFAULT_SETTINGS: {
        INITIAL_CHIPS: 100000000, // 100M chips
        COUNTDOWN_SECONDS: 5,
        BLIND_INCREASE_MINUTES: 5,
        REFUND_PERCENTAGE: 80, // 80% refund if not participated
        MAX_TOURNAMENTS_PER_TYPE_LEVEL: 1, // Maximum number of tournaments per type/level
        INITIAL_TOURNAMENTS_PER_TYPE_LEVEL: 1 // Number of initial tournaments to create per type/level at startup
    },

    // Reward distribution percentages
    REWARD_DISTRIBUTION: {
        FIRST_PLACE: 50, // 50% of prize pool
        SECOND_PLACE: 30, // 30% of prize pool
        THIRD_PLACE: 20 // 20% of prize pool
    },

    // Tournament fee structure for 5-player tournaments
    TOURNAMENT_FEES_5: {
        BEGINNER: {
            BUY_IN: 50000000, // 50M
            FEE: 5000000 // 5M
        },
        INTERMEDIATE: {
            BUY_IN: 100000000, // 100M
            FEE: 10000000 // 10M
        },
        ADVANCED: {
            BUY_IN: 200000000, // 200M
            FEE: 20000000 // 20M
        },
        PRO: {
            BUY_IN: 500000000, // 500M
            FEE: 50000000 // 50M
        }
    },

    // Tournament fee structure for 9-player tournaments
    TOURNAMENT_FEES_9: {
        BEGINNER: {
            BUY_IN: 50000000, // 50M
            FEE: 5000000 // 5M
        },
        INTERMEDIATE: {
            BUY_IN: 100000000, // 100M
            FEE: 10000000 // 10M
        },
        ADVANCED: {
            BUY_IN: 200000000, // 200M
            FEE: 20000000 // 20M
        },
        PRO: {
            BUY_IN: 500000000, // 500M
            FEE: 50000000 // 50M
        }
    },

    // Tournament fee structure for 3-player tournaments (TEST ONLY - TO BE REMOVED)
    TOURNAMENT_FEES_3: {
        TEST: {
            BUY_IN: 1000000, // 1M
            FEE: 100000 // 100K
        }
    },

    // Default blind structure
    DEFAULT_BLIND_STRUCTURE: [
        { level: 1, smallBlind: 500000, bigBlind: 1000000, ante: 0 },
        { level: 2, smallBlind: 1000000, bigBlind: 2000000, ante: 0 },
        { level: 3, smallBlind: 2000000, bigBlind: 4000000, ante: 0 },
        { level: 4, smallBlind: 4000000, bigBlind: 8000000, ante: 0 },
        { level: 5, smallBlind: 8000000, bigBlind: 16000000, ante: 100000 },
        { level: 6, smallBlind: 16000000, bigBlind: 32000000, ante: 200000 },
        { level: 7, smallBlind: 32000000, bigBlind: 64000000, ante: 300000 },
        { level: 8, smallBlind: 64000000, bigBlind: 128000000, ante: 400000 },
        { level: 9, smallBlind: 128000000, bigBlind: 256000000, ante: 500000 }
    ]
};
