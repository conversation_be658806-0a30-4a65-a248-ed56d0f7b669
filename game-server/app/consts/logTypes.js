// /app/consts/logTypes.js
// - sử dụng cho table: quest_logs >> colum: log_type: loại log
const LOG_TYPES = {
  QUEST_START: 'QUEST_START', // Bắt đầu nhiệm vụ
  QUEST_PROGRESS: 'QUEST_PROGRESS', // nhiệm vụ đang xử lý
  QUEST_COMPLETE: 'QUEST_COMPLETE', //  nhiệm vụ hoàn thành 
  QUEST_FAIL: 'QUEST_FAIL', // nhiệm vụ thất bại
  REWARD_CLAIM: 'REWARD_CLAIM', // Nhận thưởng
  EMAIL: 'EMAIL', // email // => sử dụng cho table: logs, colum: type
  SYSTEM: 'SYSTEM', // hệ thống // => sử dụng cho table: logs, colum: type
  SAFE_BOX: 'SAFE_BOX', // <PERSON><PERSON>t sắt // => sử dụng cho table: transactions, colum: type
  GAME: 'GAME', // chơi game // => sử dụng cho table: transactions, colum: type
  SNG_TOURNAMENT: 'SNG_TOURNAMENT', // Giải đấu SNG // => sử dụng cho table: transactions, colum: type
};
  
module.exports = LOG_TYPES;