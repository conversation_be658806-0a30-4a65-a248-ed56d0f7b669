/**
 * - update fillDeck
 */

var logger = require('pomelo-logger').getLogger('game-log', __filename);
var uuid = require('node-uuid');

module.exports = Game = function(smallBlind, bigBlind){
    this.id         = uuid.v1();
    this.smallBlind = smallBlind;
    this.bigBlind   = bigBlind;
    this.pot        = 0; // main pot
    this.sidePot    = 0; // side pot (deprecated, use sidepots array instead)
    this.sidepots   = []; // array of sidepots with player contributions
    this.roundName  = 'Deal'; //Start the first round
    this.betName    = 'bet'; //bet,raise,re-raise,cap
    this.bets       = [];
    this.roundBets  = [];
    this.deck       = [];
    this.board      = [];
    this.incrRaise  = bigBlind;
    this.blinds     = []; // Store SB and BB positions
    this.handNumber = 1; // Track hand number for SNG tournaments
    //fillDeck(this.deck);
    fillDeck2(this.deck);
};

function fillDeck(deck){
    deck.push('AS');
    deck.push('KS');
    deck.push('QS');
    deck.push('JS');
    deck.push('TS');
    deck.push('9S');
    deck.push('8S');
    deck.push('7S');
    deck.push('6S');
    deck.push('5S');
    deck.push('4S');
    deck.push('3S');
    deck.push('2S');
    deck.push('AH');
    deck.push('KH');
    deck.push('QH');
    deck.push('JH');
    deck.push('TH');
    deck.push('9H');
    deck.push('8H');
    deck.push('7H');
    deck.push('6H');
    deck.push('5H');
    deck.push('4H');
    deck.push('3H');
    deck.push('2H');
    deck.push('AD');
    deck.push('KD');
    deck.push('QD');
    deck.push('JD');
    deck.push('TD');
    deck.push('9D');
    deck.push('8D');
    deck.push('7D');
    deck.push('6D');
    deck.push('5D');
    deck.push('4D');
    deck.push('3D');
    deck.push('2D');
    deck.push('AC');
    deck.push('KC');
    deck.push('QC');
    deck.push('JC');
    deck.push('TC');
    deck.push('9C');
    deck.push('8C');
    deck.push('7C');
    deck.push('6C');
    deck.push('5C');
    deck.push('4C');
    deck.push('3C');
    deck.push('2C');

    //Shuffle the deck array with Fisher-Yates
    var i, j, tempi, tempj;
    for(i=0;i< deck.length;i+=1){
        j = Math.floor(Math.random() * (i + 1));
        tempi = deck[i];
        tempj = deck[j];
        deck[i] = tempj;
        deck[j] = tempi;
    }
}



function fillDeck2(deck) {
    var suits = [ 'S', 'H', 'D', 'C' ];
    var ranks = [ '2', '3', '4', '5', '6', '7', '8', '9', 'T', 'J', 'Q', 'K', 'A' ];

    var suitsLen = suits.length;
    var ranksLen = ranks.length;
    var i, j;

    for (i=0; i<suitsLen; i++) {
        for (j=0; j<ranksLen; j++) {
            deck.push( ranks[j] + suits[i] );
        }
    }

    //Shuffle the deck array with Fisher-Yates
    var i, j, tempi, tempj;
    for(i=0;i< deck.length;i+=1){
        j = Math.floor(Math.random() * (i + 1));
        tempi = deck[i];
        tempj = deck[j];
        deck[i] = tempj;
        deck[j] = tempi;
    }

    logger.info("initialCards: " + JSON.stringify(deck));
    //return deck;
}

/**
 * Calculate sidepots when a player goes all-in
 * @param {Object} game - The game object
 * @param {Array} players - Array of players in the game
 * @param {Number} allInPlayerIndex - Index of the player who went all-in
 * @return {Array} Updated sidepots array
 */
function calculateSidePots(game, players, allInPlayerIndex) {
    logger.info("Calculating sidepots after player " + players[allInPlayerIndex].playerName + " went all-in");

    // Get the all-in player's bet amount
    var allInAmount = game.bets[allInPlayerIndex];

    // Create a new sidepot
    var sidepot = {
        amount: 0,
        eligiblePlayers: [],
        allInPlayer: players[allInPlayerIndex].id
    };

    // Add all active players to the eligible list for this sidepot
    for (var i = 0; i < players.length; i++) {
        if (!players[i].folded) {
            sidepot.eligiblePlayers.push(players[i].id);
        }
    }

    // Calculate the sidepot amount
    for (var i = 0; i < game.bets.length; i++) {
        if (game.bets[i] > allInAmount) {
            // If a player bet more than the all-in amount, the excess goes to the next pot
            var excess = game.bets[i] - allInAmount;
            game.bets[i] = allInAmount;
            sidepot.amount += allInAmount;

            // The excess will be handled in the next round or main pot
            game.pot += excess;
        } else {
            // Otherwise, the entire bet goes to this sidepot
            sidepot.amount += game.bets[i];
        }
    }

    // Add the sidepot to the game's sidepots array
    game.sidepots.push(sidepot);

    logger.info("Created sidepot: " + JSON.stringify(sidepot));

    return game.sidepots;
}