var logger = require('pomelo-logger').getLogger('minigame', __filename);
var pokerHandEvaluator = require('../util/pokerHandEvaluator');
var luckyCardManager = require('../util/luckyCardManager');
var minigameConsts = require('../consts/minigameConsts');
var async = require('async');
var messageService  = require('../services/messageService');
var consts = require('../consts/consts');
var CODE = require('../consts/code');

var MinigameService = function(app) {
    this.app = app;
    this.pokerHandEvaluator = new pokerHandEvaluator();
    this.luckyCardManager = new luckyCardManager(app);
};

module.exports = MinigameService;

/**
 * Lấy thông tin luật chơi và cấu hình mini game
 * 
 * @param {Function} cb - Callback function
 */
MinigameService.prototype.getGameInfo = function(cb) {
    // Mock data
    var gameInfo = {
        name: 'Poker Spin',
        description: 'Quay bài poker để nhận thưởng',
        betOptions: [1000000, 2000000, 4000000], // 1M, 2M, 4M chips
        handTypes: minigameConsts.HAND_TYPES,
        payoutRates: minigameConsts.PAYOUT_RATES // tỉ lệ thưởng cho mỗi loại bài
    };

    process.nextTick(function() {
        cb(null, gameInfo);
    });
};

/**
 * Lấy thông tin bài may mắn ngày hôm nay
 * 
 * @param {Function} cb - Callback function
 */
MinigameService.prototype.getLuckyCard = function(cb) {
    this.luckyCardManager.getTodayLuckyCard(function(err, luckyCard) {
        if (err) {
            logger.error('[minigameService.getLuckyCard] error: %j', err);
            return cb(err);
        }
        
        cb(null, luckyCard);
    });
};

/**
 * Quay thưởng
 * 
 * @param {String} uid - ID của người chơi
 * @param {Number} betAmount - Số tiền cược
 * @param {Function} cb - Callback function
 */
MinigameService.prototype.spin = function(uid, betAmount, cb) {
    logger.info('[minigameService.spin] uid: ', uid, ' -> betAmount: ', betAmount);
    var self = this;
    
    async.waterfall([
        // 0. Lấy thông tin user
        function(callback) {
            self.app.rpc.manager.userRemote.getUserCacheByUid(null, uid, function(user) {
                logger.info('[minigameService.spin][0] >> user: ', user, ' -> betAmount: ', betAmount);
                // Kiểm tra xem số tiền cược có đủ không
                if (user.player.balance < betAmount) {
                    // return callback(new Error('Insufficient balance'));
                    return cb(new Error('Insufficient balance'));
                }
                callback(null, user);
            });
        },
        // 1. Trừ tiền cược của người chơi
        function(user, callback) {
            logger.info('[minigameService.spin][1] >> user: ', user);
            self._deductBalance(uid, betAmount, callback);
        },
        
        // 2. Khởi tạo bộ bài và lấy kết quả quay
        function(balance, callback) {
            logger.info('[minigameService.spin][2] >> balance: ', balance);
            var cards = self._getRandomCards(5);
            var handResult = self.pokerHandEvaluator.evaluateHand(cards);
            
            callback(null, {
                cards: cards,
                handType: handResult.type,
                handName: handResult.name,
                balance: balance
            });
        },
        
        // 3. Kiểm tra xem có hit bài may mắn không
        function(result, callback) {
            logger.info('[minigameService.spin][3] >> result: ', result);
            self.luckyCardManager.checkLuckyCard(result.cards, function(err, isLucky, multiplier) {
                if (err) {
                    return callback(err);
                }
                
                result.isLuckyCard = isLucky;
                result.luckyMultiplier = multiplier;
                
                callback(null, result);
            });
        },
        
        // 4. Tính toán phần thưởng
        function(result, callback) {
            logger.info('[minigameService.spin][4] >> result: ', result);
            var payoutRate = minigameConsts.PAYOUT_RATES[result.handType] || 0;
            var rewardAmount = betAmount * payoutRate;
            
            // Nhân hệ số nếu trúng bài may mắn
            if (result.isLuckyCard) {
                rewardAmount *= result.luckyMultiplier;
            }
            
            // Cộng tiền thưởng vào tài khoản người chơi
            if (rewardAmount > 0) {
                self._addReward(uid, rewardAmount, function(err, newBalance) {
                    if (err) {
                        return callback(err);
                    }
                    
                    result.reward = {
                        amount: rewardAmount,
                        payoutRate: payoutRate,
                        luckyMultiplier: result.luckyMultiplier
                    };
                    result.balance = newBalance;
                    
                    callback(null, result);
                });
            } else {
                result.reward = {
                    amount: 0,
                    payoutRate: 0,
                    luckyMultiplier: 1
                };
                
                callback(null, result);
            }
        },
        
        // 5. Lưu lịch sử quay thưởng
        function(result, callback) {
            logger.info('[minigameService.spin][5] >> result: ', result);
            // Truyền handTypeId (nếu có) vào result để lưu vào history
            if (!result.handTypeId && result.handType) {
                // Map handType thành handTypeId dựa trên minigameConsts
                const handTypeIdMap = {
                    'ROYAL_FLUSH': 1,
                    'STRAIGHT_FLUSH': 2,
                    'FOUR_OF_A_KIND': 3,
                    'FULL_HOUSE': 4,
                    'FLUSH': 5,
                    'STRAIGHT': 6,
                    'THREE_OF_A_KIND': 7,
                    'TWO_PAIR': 8,
                    'ONE_PAIR': 9,
                    'HIGH_CARD': 10
                };
                result.handTypeId = handTypeIdMap[result.handType] || 0;
            }
            
            // Gọi hàm lưu lịch sử, nhưng không trả về lỗi nếu thất bại
            self._saveSpinHistory(uid, betAmount, result, function(err) {
                if (err) {
                    logger.error('[minigameService.spin] Failed to save spin history: %j', err, ' -> message: ', err.message, ' -> stack: ', err.stack);
                    // Không return error ở đây, vẫn tiếp tục trả kết quả
                }
                
                // Luôn gọi callback với null error để tiếp tục
                callback(null, result);
            });
        }
    ], function(err, result) {
        logger.info('[minigameService.spin][6] >> result: ', result, ' -> message: ', err?.message, ' -> stack: ', err?.stack);
        if (err) {
            logger.error('[minigameService.spin] error: %j', err.message || err);
            return cb(err);
        }
        
        cb(null, result);
    });
};

/**
 * Bật/tắt chế độ tự động quay
 * 
 * @param {String} uid - ID của người chơi
 * @param {Boolean} autoSpin - Bật/tắt tự động quay
 * @param {Number} betAmount - Số tiền cược cho mỗi lượt quay tự động
 * @param {Function} cb - Callback function
 */
// MinigameService.prototype.setAutoSpin = function(uid, autoSpin, betAmount, cb) {
//     // Mock function - trong thực tế, sẽ lưu trạng thái auto spin vào DB/cache
//     process.nextTick(function() {
//         cb(null, {
//             autoSpin: autoSpin,
//             betAmount: betAmount
//         });
//     });
// };

/**
 * Lấy lịch sử quay thưởng của người chơi
 * 
 * @param {String} uid - ID của người chơi
 * @param {Number} page - Số trang
 * @param {Number} pageSize - Số lượng items mỗi trang
 * @param {Function} cb - Callback function
 */
MinigameService.prototype.getSpinHistory = function(uid, page, pageSize, cb) {
    // Mock data
    // var mockHistory = [];
    
    // for (var i = 0; i < pageSize; i++) {
    //     mockHistory.push({
    //         id: 1000 + i,
    //         time: new Date(Date.now() - i * 3600000).toISOString(),
    //         betAmount: [1000000, 2000000, 4000000][Math.floor(Math.random() * 3)],
    //         cards: this._getRandomCards(5),
    //         handType: this._getRandomHandType(),
    //         reward: Math.floor(Math.random() * 10000000),
    //         isLuckyCard: Math.random() > 0.8,
    //         luckyMultiplier: Math.random() > 0.5 ? 2 : 3
    //     });
    // }
    
    // var result = {
    //     items: mockHistory,
    //     pagination: {
    //         page: page,
    //         pageSize: pageSize,
    //         total: 100 // Mock total count
    //     }
    // };
    
    // process.nextTick(function() {
    //     cb(null, result);
    // });

    const dbManager = this.app.get('dbManager');
    dbManager.getSpinHistory({uid: uid, page: page, pageSize: pageSize}, function(err, code, result) {
        logger.info('[minigameService.getSpinHistory][0] code: ', code, ' -> result: ', result, ' -> err: ', err);
        if (err) {
            logger.error('[minigameService.getSpinHistory][1] error: ', err);
            return cb(err);
        }
        logger.info('[minigameService.getSpinHistory][2] result: ', result);
        cb(null, result);
    });
};

// ---- Private methods ----

/**
 * Lấy balance từ service coin
 * 
 * @private
 * @param {String} uid - ID của người chơi
 * @returns {Number} Balance
 */
MinigameService.prototype._getCoinBalance = async function(uid) {
    var self = this;
    const [err, code, resGetBalance] = await new Promise((resolve) => {
        // this.app.rpc.coin.coinRemote.getBalance('*', {userId: uid}, (...args) => resolve(args));
        this.app.rpc.db.dbRemote.getPlayerById('*', uid, (...args) => resolve(args));
    });
    return resGetBalance?.balance ?? 0;
}

/**
 * Trừ tiền cược từ tài khoản người chơi
 * 
 * @private
 * @param {String} uid - ID của người chơi
 * @param {Number} amount - Số tiền cược
 * @param {Function} cb - Callback function
 */
MinigameService.prototype._deductBalance = async function(uid, amount, cb) {
    var self = this;
    // Mock function - trong thực tế, sẽ gọi đến service xử lý tiền
    // Kiểm tra balance hiện tại
    // var mockCurrentBalance = 50000000; // Giả sử người chơi có 50M
    let mockCurrentBalance = 0;

    // lấy thông tin balance từ database của table: players
    // ------------------------------------------------------------------------------------------------
    try {
        const [err, code, resGetBalance] = await new Promise((resolve) => {
            // this.app.rpc.coin.coinRemote.getBalance('*', {userId: uid}, (...args) => resolve(args));
            this.app.rpc.db.dbRemote.getPlayerById('*', uid, (...args) => resolve(args));
        });
        if (err) {
            return cb(err); 
        }
        mockCurrentBalance = resGetBalance?.balance ?? 0;
        logger.info('[minigameService._deductBalance][1] >> code: ', code, ' -> balance: ', resGetBalance.balance, ' -> mockCurrentBalance: ', mockCurrentBalance);
    } catch (error) {
        return cb(error);
    }
    
    logger.info('[minigameService._deductBalance][2] >> mockCurrentBalance22: ', mockCurrentBalance);
    if (mockCurrentBalance < amount) {
        return cb(new Error('Insufficient balance'));
    }
    
    // var newBalance = mockCurrentBalance - amount;

    // cập nhật lại balance cho user
    // ------------------------------------------------------------------------------------------------
    // this.app.rpc.coin.coinRemote.cashIn('*', {userId: uid, amount: -amount, description: '[Mini game] trừ tiền cược'}, async function(err, code, resCashIn) {
    this.app.rpc.db.dbRemote.incrementPlayerStats('*', uid, { balance: -amount }, async function(err, code, resCashIn) {
        if (err) {
            return cb(err);
        }
        logger.info('[minigameService._deductBalance][3] >> err: ', err, ' -> code: ', code, ' -> resCashIn: ', resCashIn);

        // get lại số tiền từ coin service
        // ------------------------------------------------------------------------------------------------
        const currentBalance = await self._getCoinBalance(uid);
        logger.info('[minigameService._deductBalance][4] >> currentBalance: ', currentBalance);

        // cập nhật trong user cache
        // ------------------------------------------------------------------------------------------------
        self.app.rpc.manager.userRemote.decrUserCachedBalance(null, uid, amount, function (error, user) {
            logger.info("[minigameService._deductBalance][6] thong tin user from useCached: ", user, ' -> error: ', error);
        });

        // call sync playerSync create transactions
        // ------------------------------------------------------------------------------------------------
        self.app.get('sync').flush('playerSync.createTransaction', uid, {
            player_id: uid,
            amount: -amount,
            before_balance: mockCurrentBalance,
            after_balance: currentBalance,
            type: 'MINI_GAME',
            action: 'DEDUCT_BALANCE',
            reference_id: 0,
            reference_type: 'MINI_GAME',
            meta: {
                // game_id: 0,
                // table_id: 0,
                // player_name: '',
                // is_winner: false,
                // hand_info: ''
            },
            description: `Trừ tiền cược cho mini game`
        });

        // Push số tiền mới về cho người chơi
        // ------------------------------------------------------------------------------------------------
        var _cmdPush    = consts.GAME.ROUTER.UPDATE_MYSELF;
        var _msgPush    = "Update Balance";
        var _arrMsg     = {
            type: CODE.USER.BALANCE,
            msg: _msgPush,
            balance: currentBalance,
            oldBalance: mockCurrentBalance
        };
        var _code       = CODE.USER.UPDATE_MONEY;
        await messageService.pushMessageByUid(uid, _cmdPush, _arrMsg, _code);
        
        /* const channelService = this.app.get('channelService');
        const channel = channelService.getChannel(uid, false);
        if (channel) {
            channel.pushMessage(consts.GAME.ROUTER.UPDATE_MYSELF, {balance: currentBalance});
        } */

        // return balance mới
        cb(null, currentBalance);
    });
    
    // Giả lập update balance trong DB
    // process.nextTick(function() {
    //     cb(null, newBalance);
    // });
};

/**
 * Cộng tiền thưởng vào tài khoản người chơi
 * 
 * @private
 * @param {String} uid - ID của người chơi
 * @param {Number} amount - Số tiền thưởng
 * @param {Function} cb - Callback function
 */
MinigameService.prototype._addReward = function(uid, amount, cb) {
    // Mock function - trong thực tế, sẽ gọi đến service xử lý tiền
    // var mockCurrentBalance = 50000000 - amount; // Giả sử đã trừ tiền cược
    // var newBalance = mockCurrentBalance + amount;
    
    // Giả lập update balance trong DB
    // process.nextTick(function() {
    //     cb(null, newBalance);
    // });

    var self = this;
    // cập nhật lại balance cho user
    // ------------------------------------------------------------------------------------------------
    // this.app.rpc.coin.coinRemote.cashIn('*', {userId: uid, amount: amount, description: '[Mini game] nhận thưởng'}, async function(err, code, resCashIn) {
    this.app.rpc.db.dbRemote.incrementPlayerStats('*', uid, { balance: amount }, async function(err, code, resCashIn) {
        if (err) {
            return cb(err, null);
        }
        logger.info('[minigameService._addReward][1] >> resCashIn: ', resCashIn, ' -> resCashIn: ', resCashIn, ' -> err: ', err, ' -> code: ', code);

        // get lại số tiền từ coin service
        // ------------------------------------------------------------------------------------------------
        const currentBalance = await self._getCoinBalance(uid);
        logger.info('[minigameService._addReward][2] >> currentBalance: ', currentBalance);

        // cập nhật lại tiền balance trong user cache
        // ------------------------------------------------------------------------------------------------
        self.app.rpc.manager.userRemote.incrUserCachedBalance(null, uid, amount, function (error, user) {
            logger.info("[minigameService._addReward][3] thong tin user from useCached: ", user, ' -> error: ', error);
        });

        // call playerSync create transactions
        // ------------------------------------------------------------------------------------------------
        self.app.get('sync').flush('playerSync.createTransaction', uid, {
            player_id: uid,
            amount: amount,
            before_balance: currentBalance - amount,
            after_balance: currentBalance,
            type: 'MINI_GAME',
            action: 'ADD_REWARD',
            reference_id: 0,
            reference_type: 'MINI_GAME',
            meta: {
                // game_id: 0,
                // table_id: 0,
                // player_name: '',
                // is_winner: false,
                // hand_info: ''
            },
            description: `Nhận thưởng từ mini game`
        });

        // Push số tiền mới về cho người chơi
        // ------------------------------------------------------------------------------------------------
        var _cmdPush    = consts.GAME.ROUTER.UPDATE_MYSELF;
        var _msgPush    = "Update Balance";
        var _arrMsg     = {
            type: CODE.USER.BALANCE,
            msg: _msgPush,
            balance: currentBalance,
        };
        var _code       = CODE.USER.UPDATE_MONEY;
        await messageService.pushMessageByUid(uid, _cmdPush, _arrMsg, _code);

        // return balance mới
        cb(null, currentBalance);
    });
};

/**
 * Lưu lịch sử quay thưởng
 * 
 * @private
 * @param {String} uid - ID của người chơi
 * @param {Number} betAmount - Số tiền cược
 * @param {Object} result - Kết quả quay thưởng
 * @param {Function} cb - Callback function
 */
MinigameService.prototype._saveSpinHistory = function(uid, betAmount, result, cb) {
    // Gọi đến dbManager để lưu lịch sử quay
    const dbManager = this.app.get('dbManager');
    
    // Tạo một wrapper để đảm bảo callback chỉ được gọi một lần
    let callbackCalled = false;
    function onceCallback(err, code, data) {
        if (callbackCalled) {
            logger.warn('[minigameService._saveSpinHistory] Callback already called, ignoring this call');
            return;
        }
        callbackCalled = true;
        
        // Chuyển đến callback gốc
        if (cb) {
            if (err) {
                logger.error('[minigameService._saveSpinHistory] Error: %j', err.message || err);
                return cb(err);
            }
            return cb(null);
        }
    }
    
    try {
        // Chuẩn bị dữ liệu lịch sử quay
        const spinHistoryData = {
            uid: uid,
            bet_amount: betAmount,
            hand_type_id: result.handTypeId || 0, // Đảm bảo có giá trị mặc định
            hand_type: result.handType || '', // Đảm bảo có giá trị mặc định
            cards: result.cards || [],
            is_lucky_card: result.isLuckyCard || false,
            multiplier: result.luckyMultiplier || 1,
            base_reward: result.reward?.amount || 0,
            total_reward: result.reward?.amount || 0, // Sử dụng reward.amount thay vì balance
            session_status: 'COMPLETED'
        };
        
        logger.info('[minigameService._saveSpinHistory] Saving spin history with data: %j', spinHistoryData);
        
        dbManager.createSpinHistory(spinHistoryData, onceCallback);
    } catch (err) {
        logger.error('[minigameService._saveSpinHistory] Exception: %j', err);
        onceCallback(err);
    }
};

/**
 * Lấy ngẫu nhiên n lá bài
 * 
 * @private
 * @param {Number} count - Số lượng lá bài cần lấy
 * @returns {Array} Mảng các lá bài
 */
MinigameService.prototype._getRandomCards = function(count) {
    var suits = ['S', 'H', 'D', 'C']; // Spades, Hearts, Diamonds, Clubs
    var ranks = ['2', '3', '4', '5', '6', '7', '8', '9', 'T', 'J', 'Q', 'K', 'A'];
    
    var deck = [];
    for (var s = 0; s < suits.length; s++) {
        for (var r = 0; r < ranks.length; r++) {
            deck.push({
                suit: suits[s],
                rank: ranks[r],
                code: ranks[r] + suits[s] // e.g. "AS" for Ace of Spades
            });
        }
    }
    
    // Shuffle deck
    for (var i = deck.length - 1; i > 0; i--) {
        var j = Math.floor(Math.random() * (i + 1));
        var temp = deck[i];
        deck[i] = deck[j];
        deck[j] = temp;
    }
    
    return deck.slice(0, count);
};

/**
 * Lấy ngẫu nhiên một loại bộ bài (cho mock data)
 * 
 * @private
 * @returns {String} Loại bộ bài
 */
MinigameService.prototype._getRandomHandType = function() {
    var handTypes = Object.keys(minigameConsts.PAYOUT_RATES);
    var weights = handTypes.map(function(type) {
        // Hand types với tỷ lệ thưởng cao hơn sẽ có xác suất thấp hơn
        return 1 / (minigameConsts.PAYOUT_RATES[type] || 1);
    });
    
    var totalWeight = weights.reduce(function(sum, weight) {
        return sum + weight;
    }, 0);
    
    var random = Math.random() * totalWeight;
    var weightSum = 0;
    
    for (var i = 0; i < handTypes.length; i++) {
        weightSum += weights[i];
        if (random < weightSum) {
            return handTypes[i];
        }
    }
    
    return handTypes[0];
}; 