const { Model } = require('sequelize');

'use strict';

module.exports = (sequelize, DataTypes) => {
  class SngTournament extends Model {
    /**
     * Helper method for defining associations.
     * This method is not a part of Sequelize lifecycle.
     * The `models/index` file will call this method automatically.
     */
    static associate(models) {
      // define association here
      SngTournament.hasMany(models.SngTournamentPlayer, {
        foreignKey: 'tournament_id',
        as: 'players'
      });

      SngTournament.hasMany(models.SngBlindLevel, {
        foreignKey: 'tournament_id',
        as: 'blindLevels'
      });

      SngTournament.hasMany(models.SngHandHistory, {
        foreignKey: 'tournament_id',
        as: 'handHistories'
      });

      SngTournament.hasMany(models.SngReward, {
        foreignKey: 'tournament_id',
        as: 'rewards'
      });
    }
  }

  SngTournament.init({
    id: {
      type: DataTypes.BIGINT.UNSIGNED,
      primaryKey: true,
      autoIncrement: true
    },
    table_type: {
      type: DataTypes.STRING,
      allowNull: false
    },
    table_id: {
      type: DataTypes.STRING,
      allowNull: false
    },
    code: {
      type: DataTypes.STRING(100),
      unique: true
    },
    level: {
      type: DataTypes.STRING(100),
      allowNull: false
    },
    status: {
      type: DataTypes.ENUM('WAITING', 'READY', 'IN_PROGRESS', 'ENDED', 'SYSTEM', 'COMPLETED'),
      allowNull: false
    },
    player_capacity: {
      type: DataTypes.TINYINT,
      allowNull: false,
      defaultValue: 5
    },
    buy_in: {
      type: DataTypes.INTEGER.UNSIGNED,
      allowNull: false
    },
    fee: {
      type: DataTypes.INTEGER.UNSIGNED,
      allowNull: false,
      defaultValue: 0
    },
    reward_pool: {
      type: DataTypes.INTEGER.UNSIGNED,
      defaultValue: 0
    },
    created_at: {
      type: DataTypes.DATE,
      defaultValue: sequelize.literal('CURRENT_TIMESTAMP')
    },
    started_at: {
      type: DataTypes.DATE,
      allowNull: true
    },
    ended_at: {
      type: DataTypes.DATE,
      allowNull: true
    },
    metadata: {
      type: DataTypes.JSON,
      allowNull: true,
      comment: 'Additional tournament metadata like level, table_type, etc.'
    }
  }, {
    sequelize,
    modelName: 'SngTournament',
    tableName: 'sng_tournaments',
    timestamps: false
  });

  return SngTournament;
};
