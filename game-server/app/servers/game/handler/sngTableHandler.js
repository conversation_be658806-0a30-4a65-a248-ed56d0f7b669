var logger  = require('pomelo-logger').getLogger('game-log', __filename);
// var userDao = require('../../../dao/userDao');
var rooms   = require('../../../../config/data/room');
var rooms5  = require('../../../../config/data/rooms5');
var rooms9  = require('../../../../config/data/rooms9');
var _       = require('underscore');
var CODE    = require('../../../consts/code');
var consts  = require('../../../consts/consts');
const ZONES     = require('../../../consts/zones');
var sngTournaments = require('../../../../config/data/sngTournaments.json');
const LogTypes = require('../../../consts/logTypes');
const LogBoards = require('../../../consts/logBoards');
const sngConsts = require('../../../consts/sngConsts');

module.exports = function(app){
    return new Handler(app);
};
var Handler = function(app){
    this.app = app;
};
var handler = Handler.prototype;

/**
 * Lấy danh sách giải đấu SNG có sẵn
 * @param {Object} msg parameters from client
 * @param {Object} session
 * @param {Function} next callback
 */
handler.getTournaments = function(msg, session, next) {
    var me = this;
    
    logger.info("[getTournaments] msg: ", msg);
    
    var tournaments = [];
    var tableTypes = ['3_PLAYERS', '5_PLAYERS', '9_PLAYERS'];
    var levels = ['TEST', 'BEGINNER', 'INTERMEDIATE', 'ADVANCED', 'PRO'];
    
    // Create all combinations of table types and levels
    tableTypes.forEach(function(tableType) {
        levels.forEach(function(level) {
            // Find tournament config for this combination
            var playerCapacity = tableType === '5_PLAYERS' ? 5 : (tableType === '9_PLAYERS' ? 9 : 3); // Added 3 for testing
            var tournamentConfig = sngTournaments.tournaments.find(function(t) {
                return t.player_capacity === playerCapacity && t.level === level;
            });
            
            if (tournamentConfig) {
                // Calculate prize pool (buy_in * player_capacity)
                var prizePool = tournamentConfig.buy_in * tournamentConfig.player_capacity;
                
                // Calculate payout structure with actual amounts
                var payoutStructure = [
                    {
                        position: 1,
                        rank: "1st Place",
                        percentage: sngTournaments.reward_distribution.first_place,
                        amount: Math.floor(prizePool * sngTournaments.reward_distribution.first_place / 100)
                    },
                    {
                        position: 2, 
                        rank: "2nd Place",
                        percentage: sngTournaments.reward_distribution.second_place,
                        amount: Math.floor(prizePool * sngTournaments.reward_distribution.second_place / 100)
                    },
                    {
                        position: 3,
                        rank: "3rd Place", 
                        percentage: sngTournaments.reward_distribution.third_place,
                        amount: Math.floor(prizePool * sngTournaments.reward_distribution.third_place / 100)
                    }
                ];
                
                tournaments.push({
                    id: tournamentConfig.id,
                    name: tournamentConfig.name,
                    table_type: tableType,
                    level: level,
                    player_capacity: tournamentConfig.player_capacity,
                    buy_in: tournamentConfig.buy_in,
                    fee: tournamentConfig.fee,
                    total_fee: tournamentConfig.buy_in + tournamentConfig.fee,
                    initial_chips: tournamentConfig.initial_chips,
                    blind_duration_minutes: tournamentConfig.blind_duration_minutes,
                    registered_players: 0, // Will be updated below
                    players_needed: tournamentConfig.player_capacity, // Will be updated below
                    prize_pool: prizePool,
                    payout_structure: payoutStructure,
                    status: 'WAITING_FOR_PLAYERS' // Will be updated below
                });
            }
        });
    });
    
    // Get registered players count from sngTableService for each tournament type/level
    var sngTableService = me.app.get('sngTableService');
    if (!sngTableService) {
        logger.error("[sngTableHandler.getTournaments] sngTableService not available");
        return next(null, {
            code: CODE.FAIL,
            error: 'service-not-available'
        });
    }
        
    // Update registration counts for each tournament
    tournaments.forEach(function(tournament) {
        var registeredCount = sngTableService.getRegisteredPlayersCount(tournament.table_type, tournament.level);
        logger.info("[sngTableHandler.getTournaments] For ", tournament.table_type + '_' + tournament.level, "- registeredCount:", registeredCount);
        
        tournament.registered_players = registeredCount;
        tournament.players_needed = Math.max(0, tournament.player_capacity - registeredCount);
        tournament.status = registeredCount >= tournament.player_capacity ? 'READY_TO_START' : 'WAITING_FOR_PLAYERS';
    });
        
    next(null, {
        code: CODE.OK,
        route: msg.route,
        tournaments: tournaments
    });
};

/**
 * Đăng ký tham gia giải đấu SNG
 * @param {Object} msg parameters from client containing tournament_id or table_type and level
 * @param {Object} session
 * @param {Function} next callback
 */
handler.registerTournament = function(msg, session, next) {
    var me = this;
    var sngTableService = this.app.get('sngTableService');
    var uid = session.uid;
    
    logger.info("[registerTournament] msg: ", msg, " uid: ", uid);
    
    // Check if sngTableService is available
    if (!sngTableService) {
        logger.error("[registerTournament] sngTableService not available");
        return next(null, {
            code: CODE.FAIL,
            error: 'service-not-available'
        });
    }
    
    var tournamentConfig = null;
    var tableType = null;
    var level = null;
    
    // Tìm tournament config theo tournament_id hoặc table_type + level
    if (msg.tournament_id) {
        tournamentConfig = sngTournaments.tournaments.find(function(t) { 
            return t.id === msg.tournament_id; 
        });
        if (tournamentConfig) {
            tableType = tournamentConfig.player_capacity === 5 ? '5_PLAYERS' : (tournamentConfig.player_capacity === 9 ? '9_PLAYERS' : '3_PLAYERS');
            level = tournamentConfig.level;
        }
    } else if (msg.table_type && msg.level) {
        tableType = msg.table_type;
        level = msg.level;
        var playerCapacity = tableType === '5_PLAYERS' ? 5 : (tableType === '9_PLAYERS' ? 9 : 3);
        tournamentConfig = sngTournaments.tournaments.find(function(t) { 
            return t.player_capacity === playerCapacity && t.level === level; 
        });
    }
    
    if (!tournamentConfig) {
        return next(null, {
            code: CODE.FAIL,
            error: 'tournament-not-found'
        });
    }
    
    var totalFee = tournamentConfig.buy_in + tournamentConfig.fee;
    
    // Lấy thông tin người chơi để kiểm tra số dư
    me.app.rpc.db.dbRemote.getPlayerById('*', uid, function(e, code, user) {
        if (e || !user) {
            return next(null, {
                code: CODE.FAIL,
                error: 'user-not-found'
            });
        }
        
        // Kiểm tra số dư có đủ để đăng ký không
        if (user.balance < totalFee) {
            return next(null, {
                code: CODE.USER.NOT_ENOUGH_MONEY,
                error: 'insufficient-balance'
            });
        }
        
        // Đăng ký tournament thông qua sngTableService
        sngTableService.registerTournament(uid, tableType, level, function(result) {
            if (!result.success) {
                return next(null, {
                    code: CODE.FAIL,
                    error: result.error
                });
            }
                    
            // Deduct money from player balance
            me.app.rpc.db.dbRemote.updatePlayerBalance('*', uid, -totalFee, 'SNG Tournament Registration', function(updateErr, updateCode, updatePlayerBalance) {
                if (updateErr || updateCode !== CODE.OK) {
                    logger.error("[registerTournament] Failed to deduct balance: ", updateErr);
            
                    // Rollback registration
                    sngTableService.leaveTournament(uid, result.tournament_id, function(rollbackResult) {
                        logger.info("[registerTournament] Rollback registration due to balance update failure");
                    });
            
                    return next(null, {
                        code: CODE.FAIL,
                        error: 'balance-update-failed'
                    });
                }

                // ghi log transactions
                me.app.get('sync').flush('playerSync.createTransaction', uid, {
                    player_id: uid,
                    amount: -totalFee,
                    before_balance: updatePlayerBalance.old_balance,
                    after_balance: updatePlayerBalance.new_balance,
                    type: LogTypes.SNG_TOURNAMENT,
                    action: LogBoards.SNG_REGISTER,
                    reference_id: result.tournament_id,
                    reference_type: 'SNG_TOURNAMENT',
                    meta: {
                        tid: result.table_id,
                        tournament_id: result.tournament_id,
                        buy_in: tournamentConfig.buy_in,
                        fee: tournamentConfig.fee,
                        initial_chips: sngConsts.DEFAULT_SETTINGS.INITIAL_CHIPS,
                        config: tournamentConfig
                    },
                    description: 'Register for SNG Tournament',
                });
                        
                logger.info("[registerTournament] Registration result:", result, ' -> tournamentConfig: ', tournamentConfig, ' -> tableType: ', tableType);
                
                // Auto-join logic if needed
                if (result.table_id) {
                    me.autoJoinTournament(session, uid, result.table_id, tournamentConfig, function(joinErr, joinResult) {
                        logger.info("[registerTournament] Auto-join result:", joinResult);
                        logger.info("[registerTournament] Auto-join joinErr:", joinErr);
                        if (joinErr) {
                            logger.error("[registerTournament] Auto-join failed:", joinErr);
                            // Don't rollback here as registration was successful
                            next(null, {
                                code: CODE.OK,
                                route: msg.route,
                                tournament_id: result.tournament_id,
                                tournament_code: tournamentConfig?.id || '',
                                table_id: result.table_id,
                                status: result.status,
                                current_players: result.current_players || 0,
                                players_needed: result.players_needed || tournamentConfig.player_capacity,
                                message: result.status === 'tournament_started' ? 
                                    'Tournament started!' : 
                                    `Waiting for ${result.players_needed || 0} more players`,
                                auto_joined: false,
                                error: joinErr
                            });
                            return;
                        }
                        
                        // Get table info and members after successful auto-join
                        var tableInfo = null;
                        var members = [];
                        
                        if (joinResult && joinResult.success) {
                            var table = joinResult.table;
                            if (table) {
                                // Get table info
                                tableInfo = {
                                    id: table.id,
                                    tournament_id: table.tournament_id,
                                    smallBlind: table.table ? table.table.smallBlind : 0,
                                    bigBlind: table.table ? table.table.bigBlind : 0,
                                    minBuyIn: table.table ? table.table.minBuyIn : 0,
                                    maxBuyIn: table.table ? table.table.maxBuyIn : 0,
                                    minPlayers: table.table ? table.table.minPlayers : 0,
                                    maxPlayers: table.table ? table.table.maxPlayers : 0,
                                    gameMode: table.table ? table.table.gameMode : 'normal',
                                    state: table.state || 'JOIN'
                                };
                                
                                // Get members list from joinResult
                                members = joinResult.members || [];
                                
                                logger.info('[registerTournament] Auto-join successful - members count:', members.length, 'table_id:', table.id);
                            }
                        }
                        
                        next(null, {
                            code: CODE.OK,
                            route: msg.route,
                            tournament_id: result.tournament_id,
                            tournament_code: tournamentConfig?.id || '',
                            table_id: result.table_id,
                            status: result.status,
                            current_players: result.current_players || 0,
                            players_needed: result.players_needed || tournamentConfig.player_capacity,
                            message: result.status === 'tournament_started' ? 
                                'Tournament started!' : 
                                `Waiting for ${result.players_needed || 0} more players`,
                            auto_joined: !!joinResult,
                            members: members,
                            table_info: tableInfo
                        });
                    });
                } else {
                    // Nếu chưa có table_id (trường hợp waiting), vẫn push message registration success
                    var sngTableService = me.app.get('sngTableService');
                    var currentCount = sngTableService.getRegisteredPlayersCount(tableType, level);
                    
                    // Push global message cho tất cả người dùng về việc có người đăng ký mới với thông tin đầy đủ
                    var channelService = me.app.get('channelService');
                    var lobbyChannel = channelService.getChannel('lobby', false);
                    if (lobbyChannel) {
                        // Lấy thông tin player từ user cache
                        me.app.rpc.manager.userRemote.getUserCacheByUid(null, uid, function (user) {
                            logger.info("[registerTournament] User cache:", user);
                            if (user && user.player) {
                                var userPlayer = user.player;
                                lobbyChannel.pushMessage({
                                    route: consts.GAME.ROUTER.SNG_TOURNAMENT_JOIN,
                                    tournament_type: tableType,
                                    level: level,
                                    player_id: uid,
                                    player_name: userPlayer.nick_name || userPlayer.display_name || 'Player',
                                    avatar: userPlayer.avatar || '',
                                    level: userPlayer.level || 1,
                                    // display_name: userPlayer.display_name || userPlayer.nick_name || 'Player',
                                    //balance: userPlayer.balance || 0,
                                    chips: 0, // Chưa ngồi xuống bàn
                                    current_players: currentCount,
                                    players_needed: tournamentConfig.player_capacity - currentCount,
                                    status: 'WAITING_FOR_PLAYERS',
                                    timestamp: Math.floor(Date.now() / 1000)
                                });
                            }
                        });
                    }

                    next(null, {
                        code: CODE.OK,
                        route: msg.route,
                        tournament_id: result.tournament_id,
                        tournament_code: tournamentConfig?.id || '',
                        table_id: result.table_id,
                        status: result.status,
                        current_players: result.current_players || 0,
                        players_needed: result.players_needed || tournamentConfig.player_capacity,
                        message: `Waiting for ${result.players_needed || 0} more players`,
                        auto_joined: false,
                        members: [],
                        table_info: null
                    });
                }
            });
        });
    });
};

/**
 * Auto-join tournament table and sit down
 * @param {Object} session
 * @param {string} uid
 * @param {string} tableId
 * @param {Object} tournamentConfig
 * @param {Function} callback
 */
handler.autoJoinTournament = function(session, uid, tableId, tournamentConfig, callback) {
    var me = this;
    var sngTableService = this.app.get('sngTableService');
    
    if (!tableId) {
        return callback('No table ID provided');
    }
    
    logger.info("[autoJoinTournament] uid:", uid, "tableId:", tableId);
    
    // Tìm vị trí ghế trống tự động
    var availableSeat = me.findAvailableSeat(sngTableService, tableId, tournamentConfig.player_capacity);
    if (availableSeat === -1) {
        return callback('No available seat found');
    }
    
    logger.info("[autoJoinTournament] Found available seat:", availableSeat, "for uid:", uid);
    
    // Set session
    session.set('sng_tid', tableId);
    session.set('roomId', tableId);
    session.pushAll(function(sessionErr) {
        if (sessionErr) {
            logger.error('[autoJoinTournament] Set sng_tid failed:', sessionErr);
            return callback(sessionErr);
        }
                                
        // Join chat channel
        me.app.rpc.chat.chatRemote.addToChannel(session, uid, tableId, function(chatErr) {
            if (chatErr) {
                logger.error('[autoJoinTournament] Join chat channel failed:', chatErr);
                return callback(chatErr);
            }
            
            // Add as member
            sngTableService.addMember(tableId, uid, function(membersInTable) {
                logger.info("[autoJoinTournament] membersInTable: ", membersInTable);
                /* if (memberErr) {
                    logger.error('[autoJoinTournament] Add member failed:', memberErr);
                    return callback(memberErr);
                } */
                
                // Add player to table (sit down) - sử dụng addPlayer giống tableService với vị trí tự động
                sngTableService.addPlayer(tableId, uid, tournamentConfig.initial_chips, availableSeat, function(playerErr) {
                    if (playerErr) {
                        logger.error('[autoJoinTournament] Add player failed:', playerErr);
                        return callback(playerErr);
                    }
                    
                    logger.info('[autoJoinTournament] Successfully auto-joined tournament table at seat:', availableSeat);
                    
                    // Get updated members list after adding player
                    var table = sngTableService.getTable(tableId);
                    var updatedMembers = [];
                    
                    if (table && table.table) {
                        // Get members who are sitting at the table (playersToAdd)
                        updatedMembers = sngTableService.getPlayersJSON(tableId, 'playersToAdd', uid) || [];
                        logger.info('[autoJoinTournament] Updated members after addPlayer:', updatedMembers.length);
                    }
                    
                    // Push message cho event onSngTournamentJoin với thông tin player đầy đủ
                    var channelService = me.app.get('channelService');
                    var channel = channelService.getChannel(tableId, false);
                    if (channel) {
                        // Get current tournament info
                        var currentPlayers = updatedMembers.length;
                        var playersNeeded = tournamentConfig.player_capacity - currentPlayers;
                        
                        // Tìm thông tin player vừa join từ updatedMembers
                        var joinedPlayer = null;
                        for (var i = 0; i < updatedMembers.length; i++) {
                            if (updatedMembers[i].id == uid) {
                                joinedPlayer = updatedMembers[i];
                                break;
                            }
                        }
                        
                        // Fallback: lấy thông tin từ user cache nếu không tìm thấy trong members
                        if (!joinedPlayer) {
                            me.app.rpc.manager.userRemote.getUserCacheByUid(null, uid, function (user) {
                                logger.info("[autoJoinTournament] User cache:", user);
                                if (user && user.player) {
                                    var userPlayer = user.player;
                                    me.pushSngTournamentJoinMessage(channel, {
                                        tournament_id: table ? table.tournament_id : null,
                                        player_id: uid,
                                        player_name: userPlayer.nick_name || userPlayer.display_name || 'Player',
                                        avatar: userPlayer.avatar || '',
                                        level: userPlayer.level || 1,
                                        display_name: userPlayer.display_name || userPlayer.nick_name || 'Player',
                                        balance: userPlayer.balance || 0,
                                        chips: tournamentConfig.initial_chips,
                                        current_players: currentPlayers,
                                        players_needed: playersNeeded,
                                        status: playersNeeded <= 0 ? 'READY_TO_START' : 'WAITING_FOR_PLAYERS'
                                    });
                                }
                            });
                        } else {
                            logger.info("[autoJoinTournament] Joined player:", joinedPlayer);
                            // Sử dụng thông tin từ joinedPlayer
                            me.pushSngTournamentJoinMessage(channel, {
                                tournament_id: table ? table.tournament_id : null,
                                player_id: uid,
                                player_name: joinedPlayer.playerName || 'Player',
                                avatar: joinedPlayer.avatar || '',
                                level: joinedPlayer.level || 1,
                                display_name: joinedPlayer.playerName || 'Player',
                                balance: 0, // Balance đã bị trừ khi join
                                chips: joinedPlayer.chips || tournamentConfig.initial_chips,
                                current_players: currentPlayers,
                                players_needed: playersNeeded,
                                status: playersNeeded <= 0 ? 'READY_TO_START' : 'WAITING_FOR_PLAYERS'
                            });
                        }
                        
                        logger.info('[autoJoinTournament] Prepared SNG_TOURNAMENT_JOIN event, current_players:', currentPlayers, 'players_needed:', playersNeeded);
                    }
                    
                    callback(null, { 
                        success: true, 
                        members: updatedMembers,
                        table: table 
                    });
                });
            });
        });
    });
};

/**
 * Push SNG Tournament Join message với thông tin player đầy đủ
 * @param {Object} channel
 * @param {Object} playerData
 */
handler.pushSngTournamentJoinMessage = function(channel, playerData) {
    var messageData = {
        route: consts.GAME.ROUTER.SNG_TOURNAMENT_JOIN,
        tournament_id: playerData.tournament_id,
        player_id: playerData.player_id,
        player_name: playerData.player_name,
        avatar: playerData.avatar,
        level: playerData.level,
        // display_name: playerData.display_name,
        // balance: playerData.balance,
        chips: playerData.chips,
        current_players: playerData.current_players,
        players_needed: playerData.players_needed,
        status: playerData.status,
        timestamp: Math.floor(Date.now() / 1000)
    };
    
    logger.info('[pushSngTournamentJoinMessage] Pushing message:', messageData);
    channel.pushMessage(messageData);
};

/**
 * Tìm vị trí ghế trống tự động cho tournament
 * @param {Object} sngTableService
 * @param {string} tableId
 * @param {number} maxPlayers
 * @returns {number} Vị trí ghế trống (1-based) hoặc -1 nếu không tìm thấy
 */
handler.findAvailableSeat = function(sngTableService, tableId, maxPlayers) {
    var table = sngTableService.getTable(tableId);
    if (!table || !table.table) {
        logger.error('[findAvailableSeat] Table not found:', tableId);
        return -1;
    }
    
    var playersToAdd = table.table.playersToAdd || [];
    var players = table.table.players || [];
    var occupiedSeats = new Set();
    
    // Lấy danh sách ghế đã được sử dụng
    playersToAdd.forEach(function(player) {
        if (player.actorNr) {
            occupiedSeats.add(player.actorNr);
        }
    });
    
    players.forEach(function(player) {
        if (player.actorNr) {
            occupiedSeats.add(player.actorNr);
        }
    });
    
    // Tìm ghế trống đầu tiên (từ 1 đến maxPlayers)
    for (var i = 1; i <= maxPlayers; i++) {
        if (!occupiedSeats.has(i)) {
            logger.info('[findAvailableSeat] Found available seat:', i, 'for table:', tableId);
            return i;
        }
    }
    
    logger.error('[findAvailableSeat] No available seat found for table:', tableId, 'occupiedSeats:', Array.from(occupiedSeats));
    return -1;
};

/**
 * Hủy đăng ký giải đấu SNG
 * @param {Object} msg parameters from client
 * @param {Object} session
 * @param {Function} next callback
 */
handler.leaveTournament = function(msg, session, next) {
    var me = this;

    logger.info("[sngTableHandler.leaveTournament] msg: ", msg, " uid: ", session.uid);

    if (!session.uid) {
        return next(null, {
            code  : 500,
            error : 'invalid-session'
        });
    }

    if (!msg.tournamentId) {
        return next(null, {
            code: CODE.SNG.TOURNAMENT_NOT_FOUND,
            error: 'invalid-tournament-id'
        });
    }

    const sngTableService = this.app.get('sngTableService');
    var uid = session.uid;
    var tournamentId = msg.tournamentId;

    // Check if sngTableService is available
    if (!sngTableService) {
        logger.error("[sngTableHandler.leaveTournament] sngTableService not available");
        return next(null, {
            code: CODE.FAIL,
            error: 'service-not-available'
        });
    }
    
    // Hủy đăng ký tournament
    sngTableService.leaveTournament(uid, tournamentId, function(result) {
        logger.info("[sngTableHandler.leaveTournament] leaveTournament result:", result);
        if (result.success) {
            // Tìm tournament config để tính refund
            var tournamentConfig = sngTournaments.tournaments.find(function(t) { 
                return t.id === tournamentId; 
            });
            
            // var totalFee = tournamentConfig ? (tournamentConfig.buy_in + tournamentConfig.fee) : result.total_fee;
            var totalFee = tournamentConfig ? tournamentConfig.buy_in : result.total_fee;
            // Hoàn tiền cho người chơi (80% theo quy định)
            var refundAmount = Math.floor(totalFee * 0.8); // 80% refund

            logger.info("[sngTableHandler.leaveTournament] Refunding amount:", refundAmount, "for tournament:", tournamentId, "with total fee:", totalFee);

            me.app.rpc.db.dbRemote.updatePlayerBalance(session, uid, refundAmount, 'SNG Tournament Refund', function(updateErr, updateCode, updatePlayerBalance) {
                logger.info("[sngTableHandler.leaveTournament] updatePlayerBalance updateErr:", updateErr, ' -> updateCode: ', updateCode, ' -> updatePlayerBalance: ', updatePlayerBalance);

                if (updateErr) {
                    logger.error("[sngTableHandler.leaveTournament] Failed to refund balance: ", updateErr);
                }

                // Logic của leaveTable - rời khỏi bàn nếu đang ở trong bàn
                var tid = session.get('sng_tid');

                // Ghi log transactions
                me.app.get('sync').flush('playerSync.createTransaction', uid, {
                    player_id: uid,
                    amount: refundAmount,
                    before_balance: updatePlayerBalance.old_balance,
                    after_balance: updatePlayerBalance.new_balance,
                    type: LogTypes.SNG_TOURNAMENT,
                    action: LogBoards.SNG_REFUND,
                    reference_id: tournamentId,
                    reference_type: 'SNG_TOURNAMENT',
                    meta: {
                        tid: tid,
                        tournament_id: tournamentId,
                        refund_amount: refundAmount,
                        refund_percentage: sngConsts.DEFAULT_SETTINGS.REFUND_PERCENTAGE,
                        total_fee: totalFee,
                        buy_in: tournamentConfig ? tournamentConfig.buy_in : 0,
                        fee: tournamentConfig ? tournamentConfig.fee : 0,
                        config: tournamentConfig
                    },
                    description: 'Refund for SNG Tournament',
                });
                
                if (tid) {
                    logger.info("[sngTableHandler.leaveTournament] Also leaving table:", tid);
                    
                    // Leave chat channel
                    me.app.rpc.chat.chatRemote.leave(session, uid, tid, function(chatErr) {
                        if (chatErr) {
                            logger.error("[sngTableHandler.leaveTournament] Failed to leave chat channel:", chatErr);
                        }
                    });
                    
                    // Clear session
                    session.set('sng_tid', undefined);
                    session.set('roomId', undefined);
                    session.pushAll(function(sessionErr) {
                        if (sessionErr) {
                            logger.error("[sngTableHandler.leaveTournament] Failed to clear session:", sessionErr);
                        }
                        
                        // Remove member from table
                        sngTableService.removeMember(tid, uid, function(memberErr) {
                            if (memberErr) {
                                logger.error("[sngTableHandler.leaveTournament] Failed to remove member:", memberErr);
                            }
                            
                            logger.info("[sngTableHandler.leaveTournament] Successfully left tournament and table");
                            
                            next(null, {
                                code: CODE.OK,
                                route: msg.route,
                                refund_amount: refundAmount,
                                message: 'Tournament registration cancelled and left table',
                                left_table: true
                            });
                        });
                    });
                } else {
                    // Không có table để leave, chỉ hủy tournament
                    next(null, {
                        code: CODE.OK,
                        route: msg.route,
                        refund_amount: refundAmount,
                        message: 'Tournament registration cancelled',
                        left_table: false
                    });
                }
            });
        } else {
            next(null, {
                code: CODE.FAIL,
                error: result.error
            });
        }
    });
};

/**
 * Thực hiện các action trong game giống như bàn thường
 * @param {Object} msg game parameters from client
 * @param {Object} session
 * @param {Function} next callback
 */
handler.execute = function(msg, session, next) {
    logger.info("[sngTableHandler.execute] msg: ", msg);
    
    var sngTableService = this.app.get('sngTableService');
    var tid = session.get('sng_tid'); // Sử dụng SNG table ID riêng
    
    // Check if sngTableService is available
    if (!sngTableService) {
        logger.error("[sngTableHandler.execute] sngTableService not available");
        return next(null, {
            code: CODE.FAIL,
            error: 'service-not-available'
        });
    }
    
    if (!tid) {
        return next(null, {
            code: CODE.FAIL,
            error: 'not-in-sng-tournament'
        });
    }
    
    sngTableService.performAction(tid, session.uid, msg, function(e) {
        logger.info("[sngTableHandler.execute] result: ", e);
        if (e) {
            next(null, {
                code: CODE.FAIL,
                error: e
            });
            return;
        }
        next(null, {
            code: CODE.OK,
            route: msg.route
        });
    });
};

// get detail tournament
handler.getTournament = function(msg, session, next) {
    logger.info("[sngTableHandler.getTournament] msg: ", msg);

    if (!session.uid) {
        return next(null, {
            code  : 500,
            error : 'invalid-session'
        });
    }

    if (!msg.tournamentId) {
        return next(null, {
            code: CODE.FAIL,
            error: 'missing-tournament-id'
        });
    }
    
    const sngTableService = this.app.get('sngTableService');
    
    // Check if sngTournamentService is available
    if (!sngTableService) {
        logger.error("[sngTableHandler.getTournament] sngTournamentService not available");
        return next(null, {
            code: CODE.FAIL,
            error: 'service-not-available'
        });
    }
    
    sngTableService.getTournamentById(msg.tournamentId, function(e, tournament) {
        logger.info("[sngTableHandler.getTournament] tournament: ", tournament, ' -> err: ', e);
        if (e) {
            next(null, {
                code: CODE.FAIL,
                error: e
            });
            return;
        }
        const metadata = JSON.parse(tournament.metadata);
        // tournament.table_type = metadata.table_type;
        // tournament.level = metadata.level;

        const rewardPool = metadata.player_capacity * metadata.buy_in;

        const registeredCount = sngTableService.getRegisteredPlayersCount(metadata.table_type, metadata.level);
        logger.info("[sngTableHandler.getTournament] For ", metadata.table_type + '_' + metadata.level, "- registeredCount:", registeredCount);
        
        tournament.registered_players = registeredCount;
        tournament.players_needed = Math.max(0, tournament.player_capacity - registeredCount);
        // tournament.status = registeredCount >= tournament.player_capacity ? 'READY_TO_START' : 'WAITING_FOR_PLAYERS';
        tournament.reward_pool = rewardPool;
        tournament.metadata = metadata;

        next(null, {
            code: CODE.OK,
            route: msg.route,
            tournament: tournament
        });
    });
};