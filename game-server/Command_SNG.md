# Tài liệu hướng dẫn sử dụng tính năng SNG Tournament

## 1. Khởi tạo và lấy danh sách giải đấu

### 1.1. <PERSON><PERSON><PERSON> danh sách giải đấu

**<PERSON><PERSON><PERSON> sử dụng:** `game.sngTournamentHandler.getTournaments`

**<PERSON><PERSON> tả:** L<PERSON>y danh sách các giải đấu SNG hiện có

**Tham số:**

- <PERSON>hông có tham số đặc biệt

**Kết quả trả về:**

```javascript
{
  code: 200,
  tournament_types: [
    {
      type: "5_PLAYERS",
      name: "5 Players",
      levels: [
        {
          level: "BEGINNER",
          name: "<PERSON>gin<PERSON>",
          buy_in: 50000000,
          fee: 5000000,
          reward_distribution: {
            first_place:125000000,
            second_place:75000000,
            third_place:50000000
          },
          total_players: 2 // Tổng số người chơi đã đăng ký cho loại và cấp độ này
        },
        {
          level: "INTERMEDIATE",
          name: "Intermediate",
          buy_in: 50000000,
          fee: 5000000,
          total_players: 0
        },
        {
          level: "ADVANCED",
          name: "Advanced",
          buy_in: 50000000,
          fee: 5000000,
          total_players: 0
        },
        {
          level: "PRO",
          name: "Pro",
          buy_in: 50000000,
          fee: 5000000,
          total_players: 0
        }
      ]
    },
    {
      type: "9_PLAYERS",
      name: "9 Players",
      levels: [
        {
          level: "BEGINNER",
          name: "Beginner",
          buy_in: 50000000,
          fee: 5000000,
          reward_distribution: {
            first_place:125000000,
            second_place:75000000,
            third_place:50000000
          },
          total_players: 0
        },
        // Các cấp độ khác cho loại 9 người...
      ]
    }
  ]
}
```

## 2. Đăng ký và tham gia giải đấu

### 2.1. Đăng ký tham gia giải đấu

**Hàm sử dụng:** `game.sngTournamentHandler.registerTournament`

**Mô tả:** Đăng ký tham gia một giải đấu SNG. Hệ thống sẽ tự động tìm giải đấu phù hợp với loại và cấp độ được chỉ định.

**Tham số:**

- `type`: Loại giải đấu (5_PLAYERS hoặc 9_PLAYERS)
- `level`: Cấp độ giải đấu (BEGINNER, INTERMEDIATE, ADVANCED, PRO)

**Kết quả trả về:**

```javascript
{
  code: 200,
  route: "game.sngTournamentHandler.registerTournament",
  tournament_id: 1,
  tournament: {
    id: 1,
    code: "SNG_20250515_001",
    status: "WAITING",
    player_capacity: 5,
    buy_in: 50000000,
    fee: 5000000,
    reward_pool: 50000000,
    created_at: "2025-05-15T10:00:00Z",
    started_at: null,
    ended_at: null,
    registered_players: 1,
    blind_level: 1,
    current_small_blind: 500000,
    current_big_blind: 1000000,
    current_ante: 0
  },
  players: [
    {
      id: 1,
      player_id: 123,
      player_name: "Player1",
      seat_number: 0,
      initial_chips: 100000000,
      current_chips: 100000000,
      status: "ACTIVE",
      joined_at: "2025-05-15T11:30:00Z",
      avatar: "1",
      level: 5,
      vippoint: 1000,
      exp: 5000
    }
  ],
  blindLevels: [
    {
      level_number: 1,
      small_blind: 500000,
      big_blind: 1000000,
      ante: 0,
      duration_seconds: 300
    },
    // Các mức blind khác...
  ]
}
```

### 2.2. các mã lỗi

- 9000: Không tìm thấy giải đấu
- 9001: Giải đấu đã đủ người
- 9002: Giải đấu đã bắt đầu
- 9003: Giải đấu đã kết thúc
- 9004: Người chơi đã đăng ký giải đấu
- 9005: Không đủ chips để tham gia
- 9006: Loại giải đấu không hợp lệ
- 9007: Người chơi chưa đăng ký giải đấu
- 9008: Giải đấu chưa sẵn sàng
- 9009: Đã đăng ký giải đấu khác

### 2.2. Rời khỏi giải đấu

**Hàm sử dụng:** `game.sngTournamentHandler.leaveTournament`

**Mô tả:** Rời khỏi một giải đấu SNG đã đăng ký (chỉ có thể rời khi giải đấu chưa bắt đầu). Hệ thống sẽ tự động kiểm tra xem người chơi hiện tại có đang đăng ký chờ giải đấu nào không và rời khỏi giải đấu đó.

**Tham số:**

- Không cần tham số, hệ thống tự động xác định giải đấu mà người chơi đã đăng ký

**Kết quả trả về:**

```javascript
{
  code: 200,
  message: "Đã rời khỏi giải đấu",
  refund_amount: 44000000, // Số tiền hoàn lại (80% phí đăng ký)
  player: {
    id: 123,
    balance: 989000000 // Số dư sau khi hoàn phí
  }
}
```

## 3. Tham gia chơi trong giải đấu

### 3.1. Thực hiện hành động trong giải đấu

**Hàm sử dụng:** `game.sngTournamentHandler.execute`

**Mô tả:** Thực hiện các hành động trong giải đấu SNG (tương tự như trong bàn chơi thường).

**Tham số:**

- `tournamentId`: ID của giải đấu
- `action`: Hành động cần thực hiện (fold, check, call, raise, allin)
- `amount`: Số tiền cược (chỉ cần thiết cho hành động raise)

**Kết quả trả về:**

- Không trả về kết quả trực tiếp, thay vào đó sẽ gửi các sự kiện thông qua các kênh đã đăng ký (giống với bàn chơi thường)

### 3.2. Lấy thông tin chi tiết giải đấu

**Hàm sử dụng:** `game.sngTournamentHandler.getTournament`

**Mô tả:** Lấy thông tin chi tiết của một giải đấu cụ thể, bao gồm trạng thái hiện tại, người chơi đã đăng ký và cấu hình giải đấu.

**Tham số:**

- `tournamentId`: ID của giải đấu cần xem thông tin

**Kết quả trả về:**

```javascript
{
  code: 200,
  route: "game.sngTournamentHandler.getTournament",
  tournament: {
    id: "1e663270-30c5-11f0-9400-5dedf132f96b",
    tournament_id: "1e663270-30c5-11f0-9400-5dedf132f96b",
    table_id: "97c64180-2f5a-11f0-9400-5dedf132f96b",
    tournament_type: "5_PLAYERS",
    level: "BEGINNER",
    entry_fee: 50000000,
    service_fee: 5000000,
    prize_pool: 250000000,
    status: "IN_PROGRESS",
    created_at: "2025-05-15T10:30:00Z",
    started_at: "2025-05-15T10:35:00Z",
    finished_at: null,
    player_capacity: 5,
    registered_players: 5,
    players_needed: 0,
    reward_pool: 250000000,
    metadata: {
      table_type: "5_PLAYERS",
      level: "BEGINNER",
      buy_in: 50000000,
      fee: 5000000,
      player_capacity: 5,
      initial_chips: 100000000,
      blind_duration_minutes: 5
    }
  }
}
```

## 4. Đăng ký lắng nghe các sự kiện

Để nhận được các cập nhật từ giải đấu, client cần đăng ký lắng nghe các sự kiện sau:

### 4.1. Đăng ký các Event cho giải đấu

**Mô tả:** Đăng ký kênh để nhận các sự kiện liên quan đến giải đấu.

- onSngTournamentStatus: Cập nhật trạng thái giải đấu
- onSngTournamentJoin: Cập nhật danh sách người chơi khi có người tham gia
- onSngBlindUpdate: Cập nhật mức blind khi tăng
- onSngPlayerEliminated: Thông báo người chơi bị loại
- onSngTournamentResult: Kết quả giải đấu

### 4.2. Đăng ký kênh bàn chơi

**Mô tả:** Đăng ký kênh để nhận các sự kiện liên quan đến bàn chơi (tương tự như trong bàn chơi thường).

- onTableEvent: Đăng ký các sự kiện bàn chơi
- onStartTurn: Xử lý khi bắt đầu lượt chơi mới
- onEndTurn: Xử lý khi kết thúc lượt chơi
- onEndGame: Xử lý khi kết thúc ván chơi

## 5. Xử lý các sự kiện

### 5.1. Sự kiện trạng thái giải đấu (onSngTournamentStatus)

**Mô tả:** Được gửi khi trạng thái giải đấu thay đổi.

**Dữ liệu sự kiện:**

```javascript
{
  tournament: {
    id: 1,
    code: "SNG_20250515_001",
    status: "IN_PROGRESS", // WAITING, READY, IN_PROGRESS, ENDED
    player_capacity: 5,
    buy_in: 50000000,
    fee: 5000000,
    reward_pool: 250000000,
    created_at: "2025-05-15T10:00:00Z",
    started_at: "2025-05-15T12:00:00Z",
    ended_at: null,
    current_blind_level: 1,
    current_small_blind: 500000,
    current_big_blind: 1000000,
    current_ante: 0
  },
  players: [
    // Danh sách người chơi...
  ]
}
```

### 5.2. Sự kiện tăng mức blind (onSngBlindUpdate)

**Mô tả:** Được gửi khi mức blind tăng lên.

**Dữ liệu sự kiện:**

```javascript
{
  tournament_id: 1,
  level: 2,
  small_blind: 1000000,
  big_blind: 2000000,
  ante: 0,
  active_players: 4,
  total_players: 5,
  duration_seconds: 300,
  next_level_time: "2025-05-15T12:10:00Z"
}
```

### 5.3. Sự kiện người chơi bị loại (onSngPlayerEliminated)

**Mô tả:** Được gửi khi có người chơi bị loại khỏi giải đấu.

**Dữ liệu sự kiện:**

```javascript
{
  tournament_id: 1,
  player: {
    id: 1,
    player_id: 456,
    player_name: "Player2",
    seat_number: 1,
    initial_chips: 100000000,
    current_chips: 0,
    status: "ELIMINATED",
    eliminated_at_hand: 5,
    rank: 5,
    avatar: "2"
  },
  remaining_players: 4,
  total_players: 5,
  current_blind_level: 2,
  small_blind: 1000000,
  big_blind: 2000000,
  ante: 0
}
```

### 5.4. Sự kiện kết quả giải đấu (onSngTournamentResult)

**Mô tả:** Được gửi khi giải đấu kết thúc.

**Dữ liệu sự kiện:**

```javascript
{
  tournament: {
    id: 1,
    code: "SNG_20250515_001",
    status: "ENDED",
    player_capacity: 5,
    buy_in: 50000000,
    fee: 5000000,
    reward_pool: 250000000,
    created_at: "2025-05-15T10:00:00Z",
    started_at: "2025-05-15T12:00:00Z",
    ended_at: "2025-05-15T13:00:00Z"
  },
  players: [
    {
      id: 1,
      player_id: 123,
      player_name: "Player1",
      seat_number: 0,
      initial_chips: 100000000,
      current_chips: 500000000,
      status: "WINNER",
      rank: 1,
      avatar: "1",
      level: 5,
      vippoint: 1000,
      exp: 5000
    },
    {
      id: 2,
      player_id: 456,
      player_name: "Player2",
      seat_number: 1,
      initial_chips: 100000000,
      current_chips: 0,
      status: "ELIMINATED",
      eliminated_at_hand: 15,
      rank: 2,
      avatar: "2",
      level: 3,
      vippoint: 500,
      exp: 2500
    },
    // Các người chơi khác...
  ],
  rewards: [
    {
      id: 1,
      tournament_id: 1,
      player_id: 123,
      rank: 1,
      reward_amount: 125000000,
      rewarded_at: "2025-05-15T13:00:00Z",
      player_name: "Player1",
      avatar: "1"
    },
    {
      id: 2,
      tournament_id: 1,
      player_id: 456,
      rank: 2,
      reward_amount: 75000000,
      rewarded_at: "2025-05-15T13:00:00Z",
      player_name: "Player2",
      avatar: "2"
    },
    {
      id: 3,
      tournament_id: 1,
      player_id: 789,
      rank: 3,
      reward_amount: 50000000,
      rewarded_at: "2025-05-15T13:00:00Z",
      player_name: "Player3",
      avatar: "3"
    }
  ]
}
```

## 6. Luồng hoạt động tổng thể

### 6.1. Luồng đăng ký và tham gia giải đấu

1. Client lấy danh sách giải đấu bằng cách gọi `game.sngTournamentHandler.getTournaments`
2. Người chơi đăng ký tham gia giải đấu bằng cách gọi `game.sngTournamentHandler.registerTournament`
3. Client nhận sự kiện `onSngTournamentJoin` khi có người chơi mới tham gia
4. Khi đủ số lượng người chơi, giải đấu chuyển sang trạng thái READY và client nhận sự kiện `onSngTournamentStatus`
5. Hệ thống tự động tạo một giải đấu mới với cùng cấu hình để thay thế giải đấu đã đủ người
6. Sau khi đếm ngược 5 giây, giải đấu bắt đầu và chuyển sang trạng thái IN_PROGRESS

### 6.2. Luồng chơi trong giải đấu

1. Client nhận sự kiện `onTableEvent` với thông tin bàn chơi
2. Client nhận sự kiện `onStartTurn` khi bắt đầu vòng chơi mới
3. Người chơi thực hiện các hành động bằng cách gọi `game.sngTournamentHandler.execute`
4. Client nhận sự kiện `onEndTurn` khi kết thúc vòng chơi
5. Mỗi 5 phút, client nhận sự kiện `onSngBlindUpdate` khi mức blind tăng
6. Khi người chơi hết chips, client nhận sự kiện `onSngPlayerEliminated`
7. Khi chỉ còn một người chơi, giải đấu kết thúc và client nhận sự kiện `onSngTournamentResult`

### 6.3. Luồng kết thúc giải đấu

1. Giải đấu chuyển sang trạng thái ENDED
2. Client nhận sự kiện `onSngTournamentResult` với thông tin kết quả giải đấu
3. Người chơi xem kết quả và nhận thưởng (nếu có)
4. Người chơi quay lại danh sách giải đấu để tham gia giải đấu khác
