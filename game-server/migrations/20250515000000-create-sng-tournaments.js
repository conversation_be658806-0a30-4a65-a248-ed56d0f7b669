'use strict';
/** @type {import('sequelize-cli').Migration} */
module.exports = {
  async up(queryInterface, Sequelize) {
    await queryInterface.createTable('sng_tournaments', {
      id: {
        type: Sequelize.BIGINT.UNSIGNED,
        allowNull: false,
        autoIncrement: true,
        primaryKey: true
      },
      table_type: {
        type: Sequelize.STRING,
        allowNull: false
      },
      table_id: {
        type: Sequelize.STRING,
        allowNull: false
      },
      code: {
        type: Sequelize.STRING(100),
        unique: true
      },
      level: {
        type: Sequelize.STRING(100),
        allowNull: false
      },
      status: {
        type: Sequelize.ENUM('WAITING', 'READY', 'IN_PROGRESS', 'ENDED', 'SYSTEM', 'COMPLETED'),
        allowNull: false
      },
      player_capacity: {
        type: Sequelize.TINYINT,
        allowNull: false,
        defaultValue: 5
      },
      buy_in: {
        type: Sequelize.INTEGER.UNSIGNED,
        allowNull: false
      },
      fee: {
        type: Sequelize.INTEGER.UNSIGNED,
        allowNull: false,
        defaultValue: 0
      },
      reward_pool: {
        type: Sequelize.INTEGER.UNSIGNED,
        defaultValue: 0
      },
      created_at: {
        type: Sequelize.DATE,
        defaultValue: Sequelize.literal('CURRENT_TIMESTAMP')
      },
      started_at: {
        type: Sequelize.DATE,
        allowNull: true
      },
      ended_at: {
        type: Sequelize.DATE,
        allowNull: true
      },
      metadata: {
        type: Sequelize.JSON,
        allowNull: true,
        comment: 'Additional tournament metadata like level, table_type, etc.'
      }
    });
  },

  async down(queryInterface, Sequelize) {
    await queryInterface.dropTable('sng_tournaments');
  }
};
